import FontAwesome from '@expo/vector-icons/FontAwesome';
import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import * as SplashScreen from 'expo-splash-screen';
import { useEffect } from 'react';
import 'react-native-reanimated';

import { useColorScheme } from '@/components/useColorScheme';
// import FontAwesome from '@expo/vector-icons/FontAwesome';
// import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
// import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
// import * as SplashScreen from 'expo-splash-screen';
// import { useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import 'react-native-reanimated';
import { useAppContext, AppProvider } from '@/app/context/AppContext';
import { SQLiteProvider, useSQLiteContext, type SQLiteDatabase } from 'expo-sqlite';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';

export {
  // Catch any errors thrown by the Layout component.
  ErrorBoundary,
} from 'expo-router';

export const unstable_settings = {
  // Ensure that reloading on `/modal` keeps a back button present.
  initialRouteName: '(tabs)',
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded, error] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
    ...FontAwesome.font,
  });

  // Expo Router uses Error Boundaries to catch errors in the navigation tree.
  useEffect(() => {
    if (error) throw error;
  }, [error]);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return <RootLayoutNav />;
}

function NetStatus() {
  const { globalData } = useAppContext();
  return (
    <MaterialIcons 
    name={globalData.isOnline ? "wifi" : "wifi-off"} 
    size={24} 
    color={globalData.isOnline ? "#4CAF50" : "#F44336"}
    style={{ marginRight: 16 }}
  />
  );
}

function RootStacks() {
  const { globalData } = useAppContext();
  return (
    <Stack>
      <Stack.Screen name="index" options={{ 
        headerShown: false,
        title: globalData._words.appName,
        headerTitleStyle: styles.headerTitleStyle,
        headerStyle: styles.headerStyle,
        headerRight: () => <NetStatus />
        }} 
      />
      <Stack.Screen name="patrol" options={{ 
        headerShown: true,
        title: globalData._words.patrol,
        headerTitleStyle: styles.headerTitleStyle,
        headerStyle: styles.headerStyle,
        headerRight: () => <NetStatus />
        }}
      />
      <Stack.Screen name="alarm" options={{ 
        headerShown: true,
        title: globalData._words.alert,
        headerTitleStyle: styles.headerTitleStyle,
        headerStyle: styles.headerStyle,
        headerRight: () => <NetStatus />
        }} 
      />
      <Stack.Screen name="points" options={{ 
        headerShown: true,
        title: globalData._words.setPoint,
        headerTitleStyle: styles.headerTitleStyle,
        headerStyle: styles.headerStyle,
        headerRight: () => <NetStatus />
        }} 
      />
      <Stack.Screen name="changepass" options={{ 
        headerShown: true,
        title: globalData._words.changePass,
        headerTitleStyle: styles.headerTitleStyle,
        headerStyle: styles.headerStyle,
        headerRight: () => <NetStatus />
        }} 
      />
    </Stack>
  );
}

function RootLayoutNav() {
  const colorScheme = useColorScheme();
  return (
    <AppProvider>
      <RootStacks />
    </AppProvider>
  );
}

const styles = StyleSheet.create({
  headerTitleStyle: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  headerStyle: {
    backgroundColor: '#afd4f3',
  }
});