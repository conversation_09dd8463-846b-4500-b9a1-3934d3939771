{"expo": {"name": "Patrolling", "slug": "makalot-patrol", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "patrol53", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "cover", "backgroundColor": "#ffffff", "imageWidth": 400}, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["android.permission.NFC"], "package": "com.makalot.patrol53"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "react-native-nfc-manager", "expo-sqlite"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "97530436-9e9d-4fe9-8e9f-ce88427cff30"}}, "owner": "<PERSON><PERSON><PERSON>"}}