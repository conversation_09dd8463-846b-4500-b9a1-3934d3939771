// React 核心函式庫和 Hooks
import React, { useState, useEffect } from 'react';
// React Native 基礎 UI 元件
import { View, Text, StyleSheet, Modal, TouchableOpacity, TextInput, ScrollView, Image, Alert, Platform, Linking } from 'react-native';
// 應用程式全域狀態管理
import { useAppContext } from './context/AppContext';
// Expo 相機 API
import { Camera, CameraView, CameraType, useCameraPermissions } from 'expo-camera';
// Expo 位置服務 API
import * as Location from 'expo-location';
// 檔案系統操作 API
import * as FileSystem from 'expo-file-system';
// Ionicons 圖示元件
import { Ionicons } from '@expo/vector-icons';

/**
 * AlarmSend 元件屬性介面定義
 */
type AlarmSendProps = {
  visible: boolean;      // 控制 Modal 顯示/隱藏
  onClose: () => void;   // 關閉 Modal 的回調函式
  alarmType: number;     // 警報類型（1-6）
};

/**
 * AlarmSend - 警報發送元件
 *
 * 功能說明：
 * 1. 提供警報發送的完整流程介面
 * 2. 支援拍照功能並自動取得位置資訊
 * 3. 針對特定警報類型提供描述輸入
 * 4. 將警報資料儲存至本地資料庫
 * 5. 自動觸發資料上傳和通知機制
 * 6. 支援照片預覽和刪除功能
 */
const AlarmSend = ({ visible, onClose, alarmType }: AlarmSendProps) => {
  // 從全域狀態取得必要的函式和資料
  const {
    globalData,      // 全域資料（使用者資訊、組織資料、多語言文字等）
    uuidv4,          // UUID 生成函式
    formatDateTime,  // 日期時間格式化函式
    UploadAlert,     // 上傳警報資料函式
    insertAlertToDb  // 插入警報資料到資料庫函式
  } = useAppContext();

  // 狀態管理
  const [showCamera, setShowCamera] = useState(false);                    // 控制相機視圖顯示
  const [inProcess, setInProcess] = useState(false);                      // 標記是否正在處理中（拍照或發送）
  const [photos, setPhotos] = useState<Array<{                           // 照片陣列，包含 URI 和位置資訊
    uri: string;
    latitude: number | null;
    longitude: number | null;
  }>>([]);
  const [description, setDescription] = useState('');                     // 警報描述文字
  const [location, setLocation] = useState<Location.LocationObject | null>(null);  // 當前位置資訊
  const [selectedPhotoIndex, setSelectedPhotoIndex] = useState<number | null>(null);  // 選中的照片索引（用於預覽）

  /**
   * Modal 顯示狀態變更處理
   * 當 Modal 顯示時重置表單狀態
   */
  useEffect(() => {
    if (visible) {
      // 注意：不清空照片陣列，保留使用者已拍攝的照片
      // setPhotos([]);
      setDescription('');      // 清空描述文字
      setInProcess(false);     // 重置處理狀態
    }
  }, [visible]);

  /**
   * 照片陣列變更監聽
   * 用於除錯和追蹤照片狀態變化
   */
  useEffect(() => {
    console.log('照片更新:', photos);
  }, [photos]);

  /**
   * 元件初始化權限檢查和位置取得
   * 在元件載入時執行以下操作：
   * 1. 檢查並請求位置權限
   * 2. 取得當前位置資訊
   * 3. 檢查並請求相機權限
   */
  useEffect(() => {
    (async () => {
      try {
        // === 位置權限檢查和位置取得 ===
        let { status: locationStatus } = await Location.getForegroundPermissionsAsync();

        // 如果沒有位置權限，請求權限
        if (locationStatus !== 'granted') {
          locationStatus = (await Location.requestForegroundPermissionsAsync()).status;
          if (locationStatus !== 'granted') {
            Alert.alert(
              globalData._words.warning || '警告',
              '需要位置權限才能繼續操作',
              [{ text: globalData._words.ok || '確定' }]
            );
            return;
          }
        }

        // 取得當前位置資訊
        console.log('獲取位置資訊');
        let currentLocation = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Low,  // 使用低精度以加快取得速度
          timeInterval: 5000,               // 5秒時間間隔
          distanceInterval: 10              // 10公尺距離間隔
        });
        console.log('獲取位置資訊', currentLocation);
        setLocation(currentLocation);
      } catch (error) {
        console.error('權限或位置獲取失敗:', error);
        Alert.alert(
          globalData._words.warning || '警告',
          '無法獲取位置資訊',
          [{ text: globalData._words.ok || '確定' }]
        );
      }

      // === 相機權限檢查 ===
      let { status: cameraStatus } = await Camera.getCameraPermissionsAsync();

      // 如果沒有相機權限，請求權限
      if (cameraStatus !== 'granted') {
        cameraStatus = (await Camera.requestCameraPermissionsAsync()).status;
        if (cameraStatus !== 'granted') {
          Alert.alert(
            globalData._words.warning || '警告',
            '需要相機權限才能繼續操作',
            [{ text: globalData._words.ok || '確定' }]
          );
          return;
        }
      }

    })();
  }, []);

  /**
   * 清空所有照片
   * 重置照片陣列和處理狀態
   */
  const clearPhotos = () => {
    setPhotos([]);
    setInProcess(false);
  }

  /**
   * 拍照並獲取位置資訊
   *
   * 執行流程：
   * 1. 檢查相機權限，若未授權則請求權限
   * 2. 執行拍照操作（品質設為 0.5 以節省空間）
   * 3. 先將照片 URI 保存，位置資訊暫設為 null
   * 4. 關閉相機視圖
   * 5. 獲取位置權限，若授權則嘗試獲取位置
   * 6. 優先使用最後已知位置，若無則取得當前位置
   * 7. 更新最後一張照片的位置資訊
   * 8. 處理過程中任何錯誤都會顯示警告訊息
   */
  const takePicture = async () => {
    if (cameraRef.current) {
      setInProcess(true); // 標記處理中狀態，防止重複操作
      try {
        // === 相機權限檢查 ===
        let { status: cameraStatus } = await Camera.getCameraPermissionsAsync();

        if (cameraStatus !== 'granted') {
          cameraStatus = (await Camera.requestCameraPermissionsAsync()).status;
          if (cameraStatus !== 'granted') {
            Alert.alert(
              globalData._words.warning || '警告',
              '需要相機權限才能繼續操作',
              [{ text: globalData._words.ok || '確定' }]
            );
            setInProcess(false);
            return;
          }
        }

        // === 執行拍照 ===
        const photo = await cameraRef.current.takePictureAsync({ quality: 0.5 });

        // 先將照片保存到陣列，位置資訊暫設為 null
        const newPhotos = [...photos, {
          uri: photo.uri,
          latitude: null,
          longitude: null
        }];
        setPhotos(newPhotos);

        // 關閉相機視圖
        setShowCamera(false);

        // === 位置資訊取得 ===
        const { status } = await Location.requestForegroundPermissionsAsync();

        if (status === 'granted') {
          let photoLocation;
          try {
            // 優先嘗試獲取最後已知位置（速度較快）
            photoLocation = await Location.getLastKnownPositionAsync();
            if (!photoLocation) {
              // 如果沒有最後已知位置，則獲取當前位置
              photoLocation = await Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.Low,  // 使用低精度以加快速度
                timeInterval: 5000,               // 5秒時間間隔
                distanceInterval: 10              // 10公尺距離間隔
              });
            }
          } catch (error) {
            console.log('位置獲取失敗，使用備選方案:', error);
            // 備選方案：使用基本的位置獲取
            photoLocation = await Location.getCurrentPositionAsync({
              accuracy: Location.Accuracy.Low
            });
          }

          // 更新最後一張照片的位置資訊
          const updatedPhotos = newPhotos.map((p, index) => {
            if (index === newPhotos.length - 1) {
              return {
                ...p,
                latitude: photoLocation.coords.latitude,
                longitude: photoLocation.coords.longitude
              };
            }
            return p;
          });

          setPhotos(updatedPhotos);
          setInProcess(false);
        }
      } catch (error) {
        setInProcess(false);
        console.error('拍照或獲取位置時發生錯誤:', error);
        Alert.alert(
          globalData._words.warning || '警告',
          globalData._words.error || '拍照失敗',
          [{ text: globalData._words.ok || '確定' }]
        );
      }
    }
  };

  // 相機視圖參考
  const cameraRef = React.useRef<CameraView>(null);

  /**
   * 根據警報類型取得對應的名稱
   * @returns 警報類型的多語言名稱
   */
  const getAlarmTypeName = () => {
    switch (alarmType) {
      case 1:
        return globalData._words.alert1;  // 火災
      case 2:
        return globalData._words.alert2;  // 水災
      case 3:
        return globalData._words.alert3;  // 入侵
      case 4:
        return globalData._words.alert4;  // 低電量
      case 5:
        return globalData._words.alert5;  // 電力異常
      case 6:
        return globalData._words.alert6;  // 其他事件
      default:
        return '';
    }
  };

  /**
   * 處理警報發送
   *
   * 執行流程：
   * 1. 檢查資料庫是否可用
   * 2. 驗證必要輸入（如事件類型 6 需要描述）
   * 3. 顯示確認對話框
   * 4. 若使用者確認則執行：
   *    - 生成唯一的警報 ID
   *    - 將警報主要資訊存入本地資料庫
   *    - 儲存相關照片資訊到照片表
   *    - 觸發上傳程序
   *    - 根據組織設定嘗試發送簡訊或撥打電話
   * 5. 處理過程中任何錯誤都會顯示警告訊息
   */
  const handleSend = async () => {
    // === 前置檢查 ===
    // 資料庫檢查已移至 insertAlertToDb 函數中處理

    // 檢查事件類型 6（其他事件）是否有輸入描述
    if (alarmType === 6) {
      if (!description || description.trim() === '') {
        Alert.alert(
          globalData._words.warning || '警告',
          globalData._words.inputAlmsg || '請輸入事件內容',
          [{ text: globalData._words.ok || '確定' }]
        );
        return;
      }
    }

    // === 確認對話框 ===
    Alert.alert(
        globalData._words.warning || '警告',
        globalData._words.confirmAlert || '確定送出異常通報',
        [
          {
            text: globalData._words.confirm || '確定',
            onPress: async () => {
              try {
                // === 生成唯一警報 ID ===
                let guid = uuidv4();
                // 使用時間戳加上 UUID 的前 20 個字元作為警報 ID
                let alertId = ((new Date()).getTime() + guid).substring(0, 20);

                console.log('發送警報', {
                  alarmType,
                  description,
                  photos,
                  location,
                  alertId
                });

                // === 準備警報資料 ===
                const alertData = {
                  username: globalData.userInfo.UserId,           // 使用者名稱
                  alertId: alertId,                               // 警報唯一 ID
                  countryId: globalData.userInfo.CountryID,       // 國家 ID
                  factoryId: globalData.userInfo.FactoryId,       // 工廠 ID
                  pointId: '',                                    // 點位 ID（警報不特定於某個點位）
                  userId: globalData.userInfo.UserId,             // 使用者 ID
                  checkTime: formatDateTime(new Date()),          // 檢查時間
                  alarmType: alarmType,                           // 警報類型
                  alarmTime: formatDateTime(new Date()),          // 警報時間
                  latitude: location ? location.coords.latitude : null,   // 緯度
                  longitude: location ? location.coords.longitude : null, // 經度
                  description: description,                       // 警報描述
                  precId: ''                                      // 巡邏記錄 ID（空值）
                };

                // === 準備照片資料（加入 photoId） ===
                const photosWithId = photos.map(photo => ({
                  ...photo,
                  photoId: uuidv4()  // 為每張照片生成唯一 ID
                }));

                // === 使用 AppContext 函數將警報資料存入資料庫 ===
                const newId = await insertAlertToDb(alertData, photosWithId);
                console.log('警報資料已儲存，記錄 ID:', newId);

                // === 觸發資料上傳 ===
                UploadAlert('alarmSend 323');

                // === 自動通知機制 ===
                let formattedNumber = ""

                // 發送簡訊通知
                if (globalData.org.Phone) {
                  formattedNumber = globalData.org.Phone.replace(/[^\d]/g, '');  // 移除非數字字元
                  const smsUrl = `sms:${formattedNumber}${Platform.OS === 'ios' ? '&' : '?'}body=${encodeURIComponent("")}`;
                  const canOpenSms = await Linking.canOpenURL(smsUrl);

                  if (canOpenSms) {
                    await Linking.openURL(smsUrl);
                    await new Promise(resolve => setTimeout(resolve, 1500));  // 等待 1.5 秒
                  }
                }

                // 撥打電話通知
                if (globalData.org.Phone) {
                  formattedNumber = globalData.org.Phone.replace(/[^\d]/g, '');
                  const phoneUrl = `tel:${formattedNumber}`;
                  const canOpenPhone = await Linking.canOpenURL(phoneUrl);

                  if (canOpenPhone) {
                    await Linking.openURL(phoneUrl);
                  }

                  // 註解：備用的電話撥打方式（已停用）
                  // const telUrl = `tel:${formattedNumber}`;
                  // const canOpenTel = await Linking.canOpenURL(telUrl);
                  // if (canOpenTel) {
                  //   await Linking.openURL(telUrl);
                  // }
                }

                // === 顯示成功訊息 ===
                Alert.alert(
                  globalData._words.alert || 'alert',
                  globalData._words.success || 'OK',
                  [{ text: globalData._words.ok || '確定' }]
                );

                // 關閉 Modal
                onClose();
              } catch (error) {
                console.error('發送警報失敗:', error);
                Alert.alert(
                  globalData._words.warning || '警告',
                  globalData._words.error || '發送失敗',
                  [{ text: globalData._words.ok || '確定' }]
                );
              }
            }
          },
          {
            text: globalData._words.cancel || '取消',
            style: 'cancel'
          }
        ]
      );
  };

  /**
   * 刪除指定索引的照片
   * 同時從檔案系統和照片陣列中移除
   * @param index - 要刪除的照片索引
   */
  const deletePhoto = async (index: number) => {
    try {
      const fileUri = photos[index].uri;
      // 從檔案系統中刪除照片檔案
      await FileSystem.deleteAsync(fileUri);
      // 從照片陣列中移除該照片
      const newPhotos = photos.filter((_, i) => i !== index);
      setPhotos(newPhotos);
      // 關閉預覽 Modal
      setSelectedPhotoIndex(null);
    } catch (error) {
      console.error('刪除照片失敗:', error);
      Alert.alert(
        globalData._words.warning || '警告',
        '刪除照片失敗',
        [{ text: globalData._words.ok || '確定' }]
      );
    }
  };

  /**
   * 主要 UI 渲染
   * 包含警報類型顯示、描述輸入、照片功能和操作按鈕
   */
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          {/* 警報類型標題 */}
          <Text style={styles.alarmType}>
            {getAlarmTypeName()}
          </Text>

          {/* 事件描述輸入框（僅在警報類型 6 時顯示） */}
          {alarmType === 6 && (
            <TextInput
              style={styles.textInput}
              multiline={true}
              numberOfLines={4}
              value={description}
              onChangeText={setDescription}
              placeholder={globalData._words.inputAlmsg}
            />
          )}

          {/* 照片功能區域 */}
          <View style={styles.photoSection}>
            {!showCamera ? (
              <>
                {/* 照片縮圖水平滾動列表 */}
                <ScrollView
                  horizontal
                  style={styles.photoScrollView}
                  contentContainerStyle={styles.photoContainer}
                >
                  {photos.map((photo, index) => (
                    <TouchableOpacity
                      key={index}
                      onPress={() => setSelectedPhotoIndex(index)}
                    >
                      <Image
                        source={{ uri: photo.uri }}
                        style={styles.photoThumbnail}
                      />
                    </TouchableOpacity>
                  ))}
                </ScrollView>

                {/* 相機和清空照片按鈕 */}
                <View style={styles.cameraViewControls}>
                    <TouchableOpacity
                        style={[styles.touchButton, styles.cameraButton]}
                        onPress={() => setShowCamera(true)}
                    >
                        <Ionicons name="camera" size={24} color="white" />
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[styles.touchButton, styles.cameraButton]}
                        onPress={() => clearPhotos()}
                    >
                    <Ionicons name="trash" size={24} color="white" />
                    </TouchableOpacity>
                </View>
              </>

            ) : (
              /* 相機視圖 */
              <View style={styles.cameraContainer}>
                <CameraView
                  ref={cameraRef}
                  style={styles.camera}
                  facing="back"
                >
                  {/* 相機控制按鈕 */}
                  <View style={styles.cameraControls}>
                    {/* 拍照按鈕 */}
                    <TouchableOpacity
                      style={[styles.touchButton, styles.captureButton, inProcess && styles.sendButtonDisabled]}
                      disabled={inProcess}
                      onPress={takePicture}
                    >
                        <Ionicons name="checkmark" size={24} color="white" />
                    </TouchableOpacity>

                    {/* 取消相機按鈕 */}
                    <TouchableOpacity
                      style={[styles.touchButton, styles.cancelCameraButton, inProcess && styles.sendButtonDisabled]}
                      disabled={inProcess}
                      onPress={() => {
                        setShowCamera(false);
                        setInProcess(false);
                      }}
                    >
                        <Ionicons name="close" size={24} color="white" />
                    </TouchableOpacity>
                  </View>
                </CameraView>

              </View>
            )}
          </View>

          {/* 主要操作按鈕區域 */}
          <View style={styles.buttonContainer}>
            {/* 確認發送按鈕 */}
            <TouchableOpacity
              style={[
                styles.touchButton,
                styles.button,
                styles.sendButton,
                inProcess && styles.sendButtonDisabled  // 處理中時停用按鈕
              ]}
              disabled={inProcess}
              onPress={handleSend}
            >
              <Text style={styles.buttonText}>{globalData._words.confirmAlertBtn}</Text>
            </TouchableOpacity>

            {/* 取消按鈕 */}
            <TouchableOpacity
              style={[
                styles.touchButton,
                styles.button,
                styles.sendButton,
                inProcess && styles.sendButtonDisabled  // 處理中時停用按鈕
              ]}
              disabled={inProcess}
              onPress={onClose}
            >
              <Text style={styles.buttonText}>{globalData._words.cancel}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* 照片預覽 Modal */}
      <Modal
        visible={selectedPhotoIndex !== null}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setSelectedPhotoIndex(null)}
      >
        <View style={styles.previewModalContainer}>
          <View style={styles.previewModalContent}>
            {selectedPhotoIndex !== null && (
              <>
                {/* 全螢幕照片預覽 */}
                <Image
                  source={{ uri: photos[selectedPhotoIndex].uri }}
                  style={styles.previewImage}
                  resizeMode="contain"
                />

                {/* 預覽操作按鈕 */}
                <View style={styles.previewButtonContainer}>
                  {/* 刪除照片按鈕 */}
                  <TouchableOpacity
                    style={[styles.previewButton, styles.deleteButton]}
                    onPress={() => deletePhoto(selectedPhotoIndex)}
                  >
                    <Text style={styles.buttonText}>{globalData._words.delete}</Text>
                  </TouchableOpacity>

                  {/* 返回按鈕 */}
                  <TouchableOpacity
                    style={[styles.previewButton, styles.backButton]}
                    onPress={() => setSelectedPhotoIndex(null)}
                  >
                    <Text style={styles.buttonText}>{globalData._words.cancel}</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>
    </Modal>
  );
};

/**
 * 樣式定義
 * 定義警報發送元件的所有 UI 元件樣式
 */
const styles = StyleSheet.create({
  // === Modal 容器樣式 ===
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',  // 半透明黑色背景
  },

  // Modal 內容區域樣式
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    width: '100%',
    height: '100%',
  },

  // 標題樣式（目前未使用）
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },

  // 警報類型顯示樣式
  alarmType: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },

  // === 按鈕相關樣式 ===
  // 按鈕容器樣式
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    // 註解：原本的絕對定位設定（已停用）
    // position: 'absolute',
    // bottom: 80,
    // left: 0,
    // right: 0,
  },

  // 基本按鈕樣式
  button: {
    padding: 10,
    borderRadius: 5,
    width: '40%',
  },

  // 發送按鈕樣式
  sendButton: {
    backgroundColor: '#4CAF50',  // 綠色背景
  },

  // 按鈕停用狀態樣式
  sendButtonDisabled: {
    backgroundColor: '#A5D6A7',  // 較淺的綠色
    opacity: 0.6,
  },

  // 取消按鈕樣式
  cancelButton: {
    backgroundColor: '#F44336',  // 紅色背景
  },

  // 按鈕文字樣式
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
  },

  // === 輸入框樣式 ===
  textInput: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    padding: 10,
    marginBottom: 15,
    minHeight: 100,
    textAlignVertical: 'top',  // Android 上文字從頂部開始
  },

  // === 照片功能相關樣式 ===
  // 照片區域容器
  photoSection: {
    marginBottom: 15,
    height: 380,
  },

  // 照片滾動視圖
  photoScrollView: {
    maxHeight: 300,
    borderWidth: 1,
    borderColor: '#ccc',
  },

  // 照片容器
  photoContainer: {
    flexDirection: 'row',
    padding: 5,
  },

  // 照片縮圖樣式
  photoThumbnail: {
    width: 150,
    height: 150,
    marginRight: 10,
    borderRadius: 5,
  },

  // === 觸控按鈕樣式 ===
  touchButton: {
    padding: 10,
    borderRadius: 5,
    marginTop: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },

  // 相機按鈕樣式
  cameraButton: {
    backgroundColor: '#2196F3',  // 藍色背景
    marginTop: 10,
  },

  // === 相機相關樣式 ===
  // 相機容器
  cameraContainer: {
    flex: 1,
    borderRadius: 5,
    overflow: 'hidden',
  },

  // 相機視圖
  camera: {
    flex: 1,
  },

  // 相機視圖控制按鈕容器
  cameraViewControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'flex-end',
    padding: 0,
    height: 60,
  },

  // 相機內部控制按鈕容器
  cameraControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'flex-end',
    padding: 10,
    flex: 1,
    borderWidth: 1,
    borderColor: '#ccc',
  },

  // 拍照按鈕樣式
  captureButton: {
    backgroundColor: '#4CAF50',  // 綠色背景
    paddingLeft: 15,
    paddingRight: 15,
  },

  // 取消相機按鈕樣式
  cancelCameraButton: {
    backgroundColor: '#F44336',  // 紅色背景
    paddingLeft: 15,
    paddingRight: 15,
  },

  // === 照片預覽 Modal 樣式 ===
  // 預覽 Modal 容器
  previewModalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',  // 深色半透明背景
    justifyContent: 'center',
    alignItems: 'center',
  },

  // 預覽 Modal 內容
  previewModalContent: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // 預覽圖片樣式
  previewImage: {
    width: '100%',
    height: '80%',
  },

  // 預覽按鈕容器
  previewButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    padding: 20,
  },

  // 預覽按鈕基本樣式
  previewButton: {
    padding: 10,
    borderRadius: 5,
    width: '40%',
  },

  // 刪除按鈕樣式
  deleteButton: {
    backgroundColor: '#F44336',  // 紅色背景
  },

  // 返回按鈕樣式
  backButton: {
    backgroundColor: '#2196F3',  // 藍色背景
  },
});

// 匯出 AlarmSend 元件作為預設匯出
export default AlarmSend;