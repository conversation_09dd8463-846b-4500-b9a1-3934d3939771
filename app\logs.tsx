import React from 'react';
import { View, FlatList, Text, StyleSheet } from 'react-native';
import { useAppContext } from './context/AppContext';

/**
 * LogsScreen - 日誌顯示畫面元件
 * 負責顯示應用程式的操作日誌列表
 */
const LogsScreen = () => {
  // 從應用上下文獲取日誌資料和時間格式化函數
  const { logs, formatDateTime } = useAppContext();

  /**
   * renderLogItem - 渲染單一日誌項目
   * @param {Object} param0 - 包含日誌項目和索引的物件
   * @param {any} param0.item - 日誌項目資料
   * @param {number} param0.index - 日誌項目索引
   * @returns {JSX.Element} 渲染後的日誌項目元件
   */
  const renderLogItem = ({ item, index }: { item: any, index: number }) => (
    <View style={styles.logItem}>
      {/* 顯示時間戳記 (只顯示小時和分鐘) */}
      <Text style={styles.timestamp}>{formatDateTime(new Date(item.timestamp)).substring(14, 19)}</Text>
      {/* 顯示日誌訊息 (最多5行) */}
      <Text style={styles.logText} numberOfLines={5}>{item.message}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* 日誌列表 */}
      <FlatList
        data={logs} // 日誌資料來源
        renderItem={renderLogItem} // 渲染函數
        keyExtractor={(item, index) => index.toString()} // 鍵值生成器
        contentContainerStyle={styles.listContainer} // 列表容器樣式
      />
    </View>
  );
};

/**
 * styles - 元件樣式定義
 * 包含容器、列表和日誌項目的樣式設定
 */
const styles = StyleSheet.create({
  // 主容器樣式
  container: {
    flex: 1, // 填滿可用空間
    backgroundColor: '#fff', // 白色背景
  },
  // 列表容器樣式
  listContainer: {
    padding: 16, // 內邊距
  },
  // 單一日誌項目樣式
  logItem: {
    backgroundColor: '#f5f5f5', // 淺灰色背景
    padding: 4, // 內邊距
    borderRadius: 2, // 圓角
    marginBottom: 4, // 底部間距
  },
  // 日誌文字樣式
  logText: {
    fontSize: 13, // 字體大小
    color: '#333', // 深灰色文字
  },
  // 時間戳記樣式
  timestamp: {
    fontSize: 11, // 較小字體
    color: '#666', // 灰色文字
    marginTop: 4, // 頂部間距
  },
});

export default LogsScreen;
