`AppContext.tsx` 檔案使用 React 的 Context API 來管理應用程式的全域狀態和功能。

以下是其主要組成部分和流程的詳細說明：

**1. 引入 (Imports):**
   - 從 `../hooks/useDatabase` 引入 `useDatabase`：一個用於資料庫互動的自訂 Hook。
   - React 核心組件：`createContext`、`useContext`、`useState`、`useEffect`、`ReactNode`。
   - `expo-file-system` 和 `expo-sqlite`：用於 Expo 中的檔案系統操作和 SQLite 資料庫管理。

**2. 介面 (Interfaces):**
   - `AppContextType`：定義上下文值的形狀，包括：
     - `globalData`、`setGlobalData`：用於一般應用程式範圍的資料。
     - `uuidv4`：生成 UUID 的函數。
     - `formatDateTime`：格式化日期時間的函數。
     - `UploadAlert`、`endPatrol`：與警報和巡邏結束相關的函數。
     - `alertQueue`、`setAlertQueue`：用於管理警報佇列。
     - `logs`、`setLogs`、`addLog`：用於應用程式日誌記錄。
     - `appMessage`、`setAppMessage`：用於顯示應用程式訊息。
     - `settings`、`setSettings`：用於應用程式設定（例如，API URL、HTTP 標頭）。
     - `db`：SQLite 資料庫實例。
   - `LogEntry`：定義單個日誌條目的結構（時間戳和訊息）。

**3. `AppContext` 創建：**
   - `const AppContext = createContext<AppContextType | undefined>(undefined);`：創建 React 上下文。

**4. `AppProvider` 組件：**
   - 這是向其子組件提供上下文的主要組件。
   - **狀態變數：**
     - `db`：儲存 SQLite 資料庫實例。
     - `isOnline`：追蹤網路連線狀態。
     - `alertQueue`：管理待上傳的警報。
     - `logs`：儲存應用程式日誌。
     - `appMessage`：儲存用於 UI 的臨時訊息。
     - `settings`：儲存 API URL 和 HTTP 標頭。
     - `globalData`：一個大型物件，包含各種應用程式資料，如應用程式版本、登入狀態、本地化文字、使用者資訊和組織結構（國家、地區、工廠、巡邏點、使用者、巡邏記錄）。
   - **輔助函數：**
     - `formatDateTime(date: Date)`：將 Date 物件格式化為 `YYYY-MM-DD HH:MM:SS` 字串。它處理 Date 物件和日期的字串表示。
     - `uuidv4()`：生成 UUID（通用唯一識別碼）。
     - `addLog(msg: any)`：向 `logs` 狀態添加帶有時間戳的新日誌條目。
   - **`checkNetworkStatus` 函數：**
     - 異步函數，用於檢查與配置的 `apiUrl` 的連線。
     - 使用 `fetch` 向 `/connect` 端點發送 GET 請求。
     - 根據 fetch 結果更新 `isOnline` 狀態。
     - 包含網路請求的重試邏輯。
   - **`UploadAlert` 函數：**
     - 異步函數，負責從 `alertQueue` 上傳警報。
     - 遍歷 `alertQueue`，嘗試將每個警報上傳到 `apiUrl/Alert` 端點。
     - 處理成功和失敗，包括重試和將失敗的警報移回佇列。
     - 使用 `addLog` 記錄上傳嘗試和結果。
     - 成功上傳後清除 `alertQueue`。
   - **`endPatrol` 函數：**
     - 異步函數，用於標記巡邏結束。
     - 將巡邏資料 (`prs`) 發送到 `apiUrl/EndPatrol` 端點。
     - 包含重試邏輯和日誌記錄。
   - **`useEffect` Hooks：**
     - **第一個 `useEffect`（資料庫初始化）：**
       - 調用 `useDatabase()` 初始化 SQLite 資料庫。
       - 資料庫打開後設置 `db` 狀態。
       - 在組件卸載時清理資料庫連線。
     - **第二個 `useEffect`（網路狀態檢查間隔）：**
       - 設置一個間隔 (`setInterval`)，每 10 秒定期調用 `checkNetworkStatus`。
       - 在組件卸載時清理間隔。
     - **第三個 `useEffect`（警報佇列監控）：**
       - 監控 `alertQueue` 的變化。
       - 如果 `alertQueue` 不為空且 `isOnline` 為 true，則在短暫延遲後調用 `UploadAlert`。
       - 使用 `clearTimeout` 防止多次同時上傳。
     - **第四個 `useEffect`（日誌寫入檔案）：**
       - 監控 `logs` 的變化。
       - 當 `logs` 改變時，將最後 50 個日誌條目寫入應用程式文件目錄中名為 `app_log.txt` 的檔案。
   - **上下文值：**
     - `AppProvider` 返回 `AppContext.Provider`，其 `value` 屬性包含提供者中定義的所有狀態變數和函數，使其可供任何消費組件訪問。

**5. `useAppContext` Hook：**
   - `export function useAppContext()`：一個自訂 Hook，用於輕鬆消費 `AppContext`。
   - 它使用 `useContext(AppContext)`，如果在庫外使用則拋出錯誤，確保正確使用。

**整體流程：**
1. 當 `AppProvider` 組件掛載時，它會初始化 SQLite 資料庫並設置定期網路狀態檢查。
2. 它維護各種全域狀態，如 `globalData`、`settings`、`alertQueue`、`logs` 和 `appMessage`。
3. 諸如 `formatDateTime`、`uuidv4`、`addLog`、`UploadAlert` 和 `endPatrol` 等函數通過上下文提供，用於常見的應用程式操作。
4. 網路狀態持續監控，當設備在線且佇列中有待處理警報時，警報會自動上傳。
5. 應用程式日誌會持久儲存在檔案中。
6. 任何被 `AppProvider` 包裹的組件都可以使用 `useAppContext` Hook 訪問這些狀態和函數。