import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useAppContext } from './context/AppContext';
import AlarmSend from '@/app/alarmSend';

// 警報畫面主元件
// 功能：顯示不同類型的警報按鈕，並處理警報發送模組的顯示控制
const AlarmScreen = () => {
  const navigation = useNavigation();
  const { globalData } = useAppContext();
  const [modalVisible, setModalVisible] = React.useState(false);
  const [selectedAlarmType, setSelectedAlarmType] = React.useState(0);

// 處理警報按鈕點擊事件
// 參數：alarmType - 警報類型代碼(1-6)
const handleAlarmPress = (alarmType: number) => {
    setSelectedAlarmType(alarmType);
    setModalVisible(true);
  };

  return (
    <View style={styles.container}>
      {/* 管理員專用區塊 - 顯示工廠名稱 */}
      {globalData.userInfo.Admin && (
        <View style={{backgroundColor: 'gold', paddingTop: 0, paddingBottom: 6, borderRadius: 8, width: '100%'}}>
            <Text style={{fontSize: 20, fontWeight: 'bold', textAlign: 'center', marginTop: 10}}>{globalData.userInfo.FactoryName}</Text>
        </View>
      )}
        
      <View style={styles.gridContainer}>
        <View style={styles.row}>
          <TouchableOpacity style={styles.button} onPress={() => handleAlarmPress(1)}>
            <Image source={require('../assets/images/fire.png')} style={styles.image} />
            <View style={styles.buttonWrapper}>
              <Text style={styles.buttonText}>{globalData._words.alert1}</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity style={styles.button} onPress={() => handleAlarmPress(2)}>
            <Image source={require('../assets/images/flood.png')} style={styles.image} />
            <View style={styles.buttonWrapper}>
              <Text style={styles.buttonText}>{globalData._words.alert2}</Text>
            </View>
          </TouchableOpacity>
        </View>
        
        <View style={styles.row}>
          <TouchableOpacity style={styles.button} onPress={() => handleAlarmPress(3)}>
            <Image source={require('../assets/images/intruder.png')} style={styles.image} />
            <View style={styles.buttonWrapper}>
              <Text style={styles.buttonText}>{globalData._words.alert3}</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity style={styles.button} onPress={() => handleAlarmPress(4)}>
            <Image source={require('../assets/images/lobat.png')} style={styles.image} />
            <View style={styles.buttonWrapper}>
              <Text style={styles.buttonText}>{globalData._words.alert4}</Text>
            </View>
          </TouchableOpacity>
        </View>
        
        <View style={styles.row}>
          <TouchableOpacity style={styles.button} onPress={() => handleAlarmPress(5)}>
            <Image source={require('../assets/images/electric.png')} style={styles.image} />
            <View style={styles.buttonWrapper}>
              <Text style={styles.buttonText}>{globalData._words.alert5}</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity style={styles.button} onPress={() => handleAlarmPress(6)}>
            <Image source={require('../assets/images/question.png')} style={styles.image} />
            <View style={styles.buttonWrapper}>
              <Text style={styles.buttonText}>{globalData._words.alert6}</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>

      <AlarmSend
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        alarmType={selectedAlarmType}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gridContainer: {
    padding: 12,
    backgroundColor: 'gray',
    flex: 1,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 18,
  },
  button: {
    width: '49%',
    aspectRatio: .89,
    backgroundColor: 'snow',
    borderRadius: 8,
    padding: 2,
    paddingTop: 8,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  image: {
    width: '88%',
    height: '64%',
    resizeMode: 'stretch',
    marginBottom: 6
  },
  buttonWrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width:'100%'
  },
  buttonText: {
    fontSize: 14,
    color: '#333',
    textAlign: 'center',
  },
});

export default AlarmScreen;
