import { useDatabase } from '../hooks/useDatabase'; // Import the custom hook
import React, { use, createContext, useContext, useState, ReactNode, useEffect, useRef } from 'react';
import * as FileSystem from "expo-file-system";
import * as SQLite from 'expo-sqlite';
import { useUpload } from '../hooks/useUpload';

const { upload } = useUpload();
interface Settings {
  apiUrl: string;
  httpHeader: {
    "Content-Type": string;
    "Authorization": string;
  }
}

interface GlobalData {
  AppVersion: string;
  isLogin: boolean;
  isOnline: boolean;
  _words: Record<string, string>;
  version: string;
  serial: string;
  userInfo: {
    secret: { username: string; password: string };
    isLogin: boolean;
    culture: string;
    status: number;
    role: string;
    Admin: boolean;
    UserName: string;
    FactoryName: string;
    UserId: string;
    CountryID: string;
    FactoryId: string;
  };
  org: {
    Phone: string;
    Sms: string;
    countries: Array<{ CountryId: string; CountryName: string }>;
    regions: Array<{ CountryId: string; RegionId: string; RegionName: string }>;
    factories: Array<{ RegionId: string; FactoryId: string; FactoryName: string }>;
    points: Array<{ FactoryId: string; PointId: string; PointName: string; CheckOrder: number; Rfid: string; lat: number; lng: number }>;
    users: any[];
    prs: any[];
  };
}
// 定義應用程式上下文的介面，包含全域資料、狀態管理和功能函數
interface AppContextType {
  globalData: GlobalData;
  setGlobalData: (data: any) => void;
  uuidv4: () => string;
  formatDateTime: (date: Date) => string;
  UploadAlert: (caller:string) => void;
  endPatrol: (prs: any) => void;
  alertQueue: any[];
  setAlertQueue: (data: any[]) => void;
  logs: LogEntry[];
  setLogs: (data: LogEntry[]) => void;
  appMessage: string;
  setAppMessage: (message: string) => void;
  settings: Settings;
  setSettings: (data: any) => void;
  addLog: (msg: any) => void;
  updateUserInfoInDb: (userInfo: any, words: Record<string, string>) => Promise<void>;
  insertAlertToDb: (alertData: any, photos: any[]) => Promise<number>;
  db: SQLite.SQLiteDatabase | null;
}

// 定義日誌項目的介面結構
export interface LogEntry {
  timestamp: number;
  message: string;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

// 提供應用程式上下文的元件，管理全域狀態和功能
export function AppProvider({ children }: { children: ReactNode }) {
  // 資料庫狀態管理
  const [db, setDb] = useState<SQLite.SQLiteDatabase | null>(null);
  const [isOnline, setIsOnline] = useState(false);
  const [alertQueue, setAlertQueue] = useState<any[]>([]);
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [appMessage, setAppMessage] = useState('');
  const [uploadTrigger, setUploadTrigger] = useState(0);

  // Use useRef to persist uploadTimeoutId across renders
  const uploadTimeoutId = useRef<number | null>(null);
  const endPatrolTimeoutId = useRef<number | null>(null);
  const uploadInProcess = useRef<number | null>(0);
  const maxRetries = 2; // 最大重試次數

 
  // 應用程式設定，包含API網址和請求標頭
  const [settings, setSettings] = useState({
    apiUrl: "https://alertsystem.makalot.com.tw/AppPatrol",
    httpHeader: {
        "Content-Type": "application/json",
        "Authorization": "MAKALOTPATROL$&^%*&JF5a66asdlfjJDFl"
    }
  });

  const serverAddress = useRef(settings.apiUrl);

  // 監聽 settings.apiUrl 變化並自動更新資料庫並更新 serverAddress 參考
  useEffect(() => {
    serverAddress.current = settings.apiUrl; //更新 serverAddress 參考

    const updateApiUrlInDatabase = async () => {
      if (db && settings.apiUrl) {
        try {
          console.log('自動更新資料庫中的 API URL:', settings.apiUrl);
          await db.runAsync('UPDATE userInfo SET apiUrl = ?', [settings.apiUrl]);
          console.log('資料庫 API URL 更新完成');
          addLog(`API URL 已更新至: ${settings.apiUrl}`);
        } catch (error) {
          console.error('更新資料庫 API URL 失敗:', error);
          addLog(`更新 API URL 失敗: ${error}`);
        }
      }
    };

    // 只有在資料庫準備好且 apiUrl 不是初始值時才執行更新
    if (db) {
      updateApiUrlInDatabase();
    }
  }, [settings.apiUrl]);

  
  // 全域資料狀態，包含應用程式版本、使用者資訊、組織結構等
  const [globalData, setGlobalData] = useState<any>(
    { 
      AppVersion: '3.0',

      isLogin: false,
      isOnline: false,
      // _setting: {
      //   apiUrl: "http://182.233.38.112:8020/AppPatrol",
      //   httpHeader: {
      //       "Content-Type": "application/json",
      //       "Authorization": "MAKALOTPATROL$&^%*&JF5a66asdlfjJDFl"
      //   }
      // },
      _words: {
        appName: "廠區巡邏系統",
        patrol: "巡邏作業",
        country: "產區",
        region: "廠區",
        factory: "工廠",
        point: "巡邏點",
        userid: "帳號",
        password: "密碼",
        changePass: "變更密碼",
        signin: "登入",
        signout: "登出",
        exitHint: "確定離開系統 ?",
        serverOut: "伺服器離線",
        newpwd: "新密碼",
        oldpwd: "原密碼",
        confpwd: "確認密碼",
        confirm: "確定",
        ok: "確定",
        delete: "刪除",
        cancel: "取消",
        required: "必須輸入",
        pwdMismatch: "新密碼與確認密碼不相同",
        potral: "巡邏作業",
        setPoint: "巡邏點RFID設定",
        setPatrol: "巡邏員RFID設定",
        arriveScan: "巡邏簽到",
        thisPoint: "巡邏點 :",
        checkTime: "巡邏時間:",
        nextPoint: "下個巡邏點:",
        leftPoint: "剩餘巡邏點數:",
        alert1: "火災",
        alert2: "水災",
        alert3: "人員入侵",
        alert4: "停電",
        alert5: "未關電",
        alert6: "其他",
        explain: "請說明",
        notInTime: "尚未進入巡邏期間",
        openCamera: "開啟相機",
        confirmAlertBtn: "確定送出",
        inputAlmsg: "請輸入事件內容",
        confirmAlert: "確定送出異常通報 ?",
        expired: "APP已經更新，請安裝最新版本",
        changeServer: "變更伺服器",
        success: "成功",
        failed: "失敗",
        missOrd: "巡邏順序錯誤",
        reScan: "請重新簽到"
      },
      version: '29',
      serial: '001',
      userInfo : {
        secret: { username: "", password: "" },
        isLogin: false,
        culture: "zh-TW",
        status: 0,
        role: "",
        Admin: false,
        UserName: ""
      },
      org: {
        Phone: "",
        Sms: "",
        countries: [
            { CountryId: "1", CountryName: "台灣" },
        ],
        regions: [
            { CountryId: "1", RegionId: "1", RegionName: "台灣北部" },
        ],
        factories: [
            { RegionId: "1", FactoryId: "1", FactoryName: "台灣北部工廠1" },
        ],
        points: [
            { FactoryId: "1", PointId: "1", PointName: "工廠1巡邏點1", CheckOrder: 1 },
        ],
        users: [],
        prs: [],
      },
    }
  );

  // 格式化日期時間為字串
  const formatDateTime = (date: Date): string => {
    // console.log('formatDateTime date', date);
    // console.log('typeof(date)', typeof(date));
    if (typeof(date) == 'string') {
        date = new Date(date);
    }
    const pad = (n: number) => n.toString().padStart(2, '0');
    return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
  };

  // 生成唯一識別碼
  const uuidv4 = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  // 添加日誌記錄
  const addLog = (msg: any) => {
    setLogs((prevLogs: LogEntry[]) => {
      const newLogs = [
        ...prevLogs,
        {
          timestamp: Date.now(),
          message: msg || ""
        }
      ];
      // 如果超過最大數量，移除最舊的日誌
      return newLogs.length > CONFIG.MAX_LOG_ENTRIES 
        ? newLogs.slice(newLogs.length - CONFIG.MAX_LOG_ENTRIES) 
        : newLogs;
    });
  }

  // 檢查網路連線狀態
  const checkNetworkStatus = async () => {
    // console.log('checkNetworkStatus...', serverAddress.current, settings.apiUrl);
    let responseOk = false;
    let currentApiUrl = serverAddress.current;
    // setSettings((prev: any) => {
    //   currentApiUrl = prev.apiUrl;
    //   return prev;
    // });
    try {
      const response = await fetch(currentApiUrl + "/connect", {
        method: 'POST',
        headers: settings.httpHeader,
        body: JSON.stringify({}),
      });

      responseOk = response.ok;

    } catch (error) {
        responseOk = false;
        // setAppMessage(currentApiUrl + "/connect" + JSON.stringify(error));
        addLog(currentApiUrl + "/connect:" + JSON.stringify(error));
        // console.log('checkNetworkStatus...', false);
        setGlobalData((prev: any) => ({
            ...prev,
            isOnline: false
        }));
    }

    // setAppMessage('rOk.' + responseOk + new Date().toLocaleString());
    // console.log('net stat ...', responseOk);
    setIsOnline((prev: boolean) => {
        if (prev != responseOk) {
          setGlobalData((prev: any) => ({
            ...prev,
            isOnline: responseOk
          }));
          // setLogs((prevLogs: any[]) => [
          //   ...prevLogs,
          //   {
          //     timestamp: Date.now(),
          //     message: 'setGlobalData isOnline=' + responseOk
          //   }
          // ]);
        }
        return responseOk;
    });
  };

  useEffect(() => {
      const openDatabase = async () => {
        try {
          const database = await SQLite.openDatabaseAsync('patrol.db');
          // Optional: Initialize your tables here
          await database.execAsync(`
            PRAGMA journal_mode = WAL;
            CREATE TABLE IF NOT EXISTS alerts (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              Guid TEXT, PrecId TEXT, username TEXT,
              CountryId TEXT, FactoryId TEXT, PointId TEXT, UserId TEXT,
              CheckTime TEXT,
              AlarmType INTEGER, AlarmTime TEXT,
              Latitude REAL, Longitude REAL,
              AlarmDesc TEXT, Photo INTEGER,
              trys INTEGER, upload INTEGER
            );
            
            CREATE TABLE IF NOT EXISTS alertfiles (
              id INTEGER PRIMARY KEY,
              alertId INTEGER,
              path TEXT,
              PhotoId TEXT,
              time TEXT,
              Latitude REAL,
              Longitude REAL
            );
            
            CREATE TABLE IF NOT EXISTS userInfo (
              id INTEGER PRIMARY KEY,
              info TEXT,
              apiUrl TEXT,
              words TEXT
            );
          `);

          console.log('inside openDatabase db', database);
          await afterOpenDatabase(database);
          setDb(database);

          console.log('await checkNetworkStatus()', serverAddress.current, settings.apiUrl);
          await checkNetworkStatus();
          console.log('finish checkNetworkStatus()');

          setUploadTrigger(prev => prev + 1); // Trigger uploadAlert on initial load

        } catch (err) {
          console.error('Failed to open database or initialize tables:', err);
        }

      };
      openDatabase();
 
      const afterOpenDatabase = async (database: SQLite.SQLiteDatabase) => {
        if (!database) {
          return;
        }
        console.log('SELECT info,words,apiUrl FROM userInfo');
      //   database.getFirstAsync('SELECT info,words,apiUrl FROM userInfo').then((result: any) => {
      //     if (result == null) {
      //       if (!database) return;
      //       console.log('INSERT INTO userInfo');
      //       database.runAsync('INSERT INTO userInfo (info,words,apiUrl) VALUES (?,?,?)', 
      //         [JSON.stringify(globalData.userInfo), JSON.stringify(globalData._words), settings.apiUrl]);
      //     } else {
  
      //       setGlobalData((prev: any) => ({
      //         ...prev,
      //         userInfo: JSON.parse(result.info),
      //         _words: JSON.parse(result.words)
      //       }));

      //       serverAddress.current = result.apiUrl;
      //       console.log('userInfo.apiUrl', serverAddress.current);
      //       setSettings((prev: any) => ({
      //         ...prev,
      //         apiUrl: result.apiUrl
      //       }));
      //     }
      //   });      
      const result: any = await database.getFirstAsync('SELECT info,words,apiUrl FROM userInfo');
      if (result == null) {
        console.log('INSERT INTO userInfo');
        await database.runAsync(
          'INSERT INTO userInfo (info,words,apiUrl) VALUES (?,?,?)',
          [JSON.stringify(globalData.userInfo), JSON.stringify(globalData._words), settings.apiUrl]
        );
      } else {
        setGlobalData((prev: any) => ({
          ...prev,
          userInfo: JSON.parse(result.info),
          _words: JSON.parse(result.words)
        }));

        serverAddress.current = result.apiUrl;
        console.log('userInfo.apiUrl', serverAddress.current);
        setSettings((prev: any) => ({
          ...prev,
          apiUrl: result.apiUrl
        }));
      }
    }

    const intervalId = setInterval(checkNetworkStatus, 15000);

    return () => {
      clearInterval(intervalId);
      if (uploadTimeoutId.current) {
        clearTimeout(uploadTimeoutId.current);
      }
      if (endPatrolTimeoutId.current) {
        clearTimeout(endPatrolTimeoutId.current);
      }
    };
}, []);

  useEffect(() => {
      console.log('useEffect uploadTrigger >>> ' + uploadTrigger);
      if (uploadTrigger > 0) {
        uploadAlert_();
      }
  }, [uploadTrigger]);

  useEffect(() => {
    if (globalData.isLogin) {
      UploadAlert('login callback');
    }
  }, [globalData.isLogin]);

  // 上傳警報資訊到伺服器
  /**
 * 上傳警報的入口函數
 * @param caller 呼叫來源標識，用於追蹤呼叫來源
 * 功能說明：
 * 1. 清除之前設定的上傳計時器
 * 2. 生成唯一時間標識(tid)
 * 3. 呼叫實際的上傳函數uploadAlert_
 */
const UploadAlert = async (caller:string) => {
  if (uploadInProcess.current == 1) {
    console.log(' &&&&&&& uploadInProcess from ' + caller); // Access current value
    return;
  }
  let tid = new Date().getTime();
  uploadAlert_();
}


  /**
 * 實際執行警報上傳的核心函數
 * @param tid 時間標識，用於追蹤同一批次的上傳請求
 * 功能流程：
 * 1. 記錄日誌
 * 2. 從資料庫讀取待上傳警報
 * 3. 設定重試機制(maxRetries次)
 * 4. 格式化警報資料
 * 5. 嘗試上傳到API端點
 * 6. 根據API回應處理結果(成功/失敗/刪除)
 * 7. 更新資料庫狀態
 */
const uploadAlert_ = async () => {
    // 警報上傳主函數
    // tid: 時間標記，用於追蹤上傳流程
    console.log('((((uploadAlert_ ========== ' + new Date().toLocaleString() + ' ))))');
    addLog('uploadAlert_');

    // console.log(`$$$$$ prs.length=${globalData.org.prs.length}`);
      
    if (!!uploadTimeoutId.current) { // Check current value
        // 清除上一個 setTimeout 計時器
        console.log(`      clearTimeout(${uploadTimeoutId.current})`); // Clear current value
        clearTimeout(uploadTimeoutId.current);
        uploadTimeoutId.current = null; // Reset to null
    }

    if (uploadInProcess.current !== 0) {
      console.log(' &&&&&&& uploadInProcess  ', uploadInProcess.current); // Access current value
      return;
    }

    // 檢查資料庫連接狀態
    if (!db) {
      console.log('Database not yet loaded or db is null.');
      return;
    }
    // 從資料庫查詢所有未上傳的警報記錄
    // 按ID排序以確保先處理最早的警報
    const allAlerts = await db.getAllAsync<{
        id: number;
        upload: number; // 上傳狀態 (0=未上傳, 1=已上傳)
        Guid: string;  // 警報唯一識別碼
        CheckTime: any; // 檢查時間
        AlarmTime: any; // 警報時間
        PrecId: string; // 巡檢點ID
        UserId: string; // 用戶ID
        AlarmType: number; // 警報類型
        }>(
        'SELECT * FROM alerts order by id'
    );

    console.log(`setAlertQueue 資料數 : ${allAlerts.length}`);
    setAlertQueue(allAlerts); // 更新警報陣列狀態
    if (allAlerts.length == 0) {
        console.log('uploadAlert_ alerts is null');
        // setTimeout(() => {
        //   setUploadTrigger(prev => prev + 1); // Trigger next upload
        // }, 35000); // 處理完畢，檢查是否有下一筆資料
        return;
    }
    
    // 取出第一筆警報資料
    const alerts = allAlerts[0];
    console.log('uploadAlert_ alerts', alerts);
    // console.log('typeof(alerts.CheckTime)', typeof(alerts.CheckTime));
    // console.log('typeof(alerts.AlarmTime)', typeof(alerts.AlarmTime));

    const checkTime = new Date(alerts.CheckTime);
    const totalHours = Math.floor((new Date().getTime() - checkTime.getTime()) / (1000 * 60 * 60));

    const alertData = {
        ...alerts,
        CheckTime: formatDateTime(alerts.CheckTime),
        AlarmTime: formatDateTime(alerts.AlarmTime),
    };

    let retObj = { 
        AlarmType: 0, 
        AlarmTime: '', 
        Latitude: 0, 
        Longitude: 0, 
        PrecId: null, 
        Photo: 0, 
        upload: 0, 
        trys: 0, 
        success: false, 
        message: '', 
        errmsg: ''
    };

    try {
        if (alerts.upload == 0) {
            console.log('alerts.upload == 0');
            uploadInProcess.current = 1;
            // retObj = await tryUpload(alertData, "UploadAlert");
            retObj = await upload(alertData, serverAddress.current + "/UploadAlert", settings.httpHeader, addLog, CONFIG);
            console.log('retObj', retObj);
            uploadInProcess.current = 0;

            addLog(`retObj.success=${retObj.success}`);

            // 處理上傳結果
            if (retObj.success !== true) {
              if (retObj.message == "delete") {
                // 伺服器要求刪除此警報記錄
                db.runAsync('DELETE FROM alerts WHERE id = ?', [alerts.id]);
                console.log('message == delete, 取消上傳');
                let prs = globalData.org.prs.find((e: any) => {
                    return e.PrecId == alerts.PrecId;
                });
                if (prs != null) {
                    prs.upload = false;
                    prs.CheckTime = null;
                    prs.msg = retObj.errmsg;
                    console.log('取消上傳成功', prs);
                }
              } else {
                  // 上傳失敗，20秒後重試
                  console.warn('Upload success error 15秒重試');
                  uploadTimeoutId.current = setTimeout(() => { setUploadTrigger(prev => prev + 1) }, 15000); // Assign to current
                  return;
              }
            } else {
              //update upload to 1, 下次不再上傳
              db.runAsync('UPDATE alerts SET upload = 1 WHERE id = ?', [alerts.id]);
              console.log('UPDATE alerts SET upload = 1');
              if (alertData.AlarmType == 0 || alertData.AlarmType == null) {
                // addLog('*retObj.success');
                console.log('retObj.success', retObj.success);
                console.log('alertData.PrecId', alertData.PrecId);
                console.log('retObj.PrecId', retObj.PrecId);
                if (retObj.success && alertData.PrecId == retObj.PrecId) {
                    console.log('如果是重新傳送，必須寫入手機端的簽到時間', retObj.PrecId);
                    // setLogs((prevLogs: LogEntry[]) => [
                    //   ...prevLogs,
                    //   {
                    //     timestamp: Date.now(),
                    //     message: '網路斷線之後重新傳送，必須寫入簽到時間'
                    //   }
                    // ]);

                    // 更新全域資料中的巡檢點簽到時間                    
                    let Prs = globalData.org.prs.find((e: any) => e.PrecId === retObj.PrecId);
                    console.log('全域資料巡檢點 globalData.org.prs', globalData.org.prs.length);
                    console.log('search result', Prs);
                    if (Prs != null) {
                        if (!Prs.CheckTime) Prs.CheckTime = new Date(alertData.CheckTime);
                        Prs.UserId = alertData.UserId;
                        Prs.upload = true;
                        globalData.org.prs.forEach((e: any) => {
                          if (e.Code == Prs.Code && e.StartTime == Prs.StartTime) {
                            // console.log('指定userid', e.UserId, alertData.UserId);
                            if (e.UserId == null) e.UserId = alertData.UserId;
                          }
                        });

                        setGlobalData({
                          ...globalData,
                          org: {
                            ...globalData.org,
                            prs: globalData.org.prs,
                          },
                        });
                        // setLogs((prevLogs: LogEntry[]) => [
                        //   ...prevLogs,
                        //   {
                        //     timestamp: Date.now(),
                        //     message: '寫入簽到時間成功'
                        //   }
                        // ]);
    
                        console.log(`寫入簽到時間成功 `, Prs);
                    } 
                }            
              }
            }

        }

        // 處理異常通報(AlarmType > 0)的照片上傳
        if (alertData.AlarmType > 0) {
          // 查詢此警報相關的所有照片記錄
          const allRows = await db.getAllAsync<{
              alertId: number;   // 警報ID
              PhotoId: string;   // 照片唯一識別碼
              path: string;      // 照片檔案路徑
              Latitude: number;  // 拍攝緯度
              Longitude: number; // 拍攝經度
              time: string;      // 拍攝時間
          }>('SELECT * FROM alertfiles where alertId = ?', [alerts.id]);
          
          if (allRows.length > 0) {
              let formData = new FormData();
              const mappedRows = allRows.map(row => {
                  formData.append('files', {
                      uri: row.path,
                      name: `${row.PhotoId}.jpg`,
                      type: 'image/jpeg',
                  } as any);
                  return {
                      AlertId: alerts.Guid,
                      CountryId: globalData.userInfo.CountryID,
                      UserId: globalData.userInfo.UserId,
                      PhotoId: row.PhotoId,
                      TakeTime: row.time,
                      Latitude: row.Latitude,
                      Longitude: row.Longitude
                  };
              });
              formData.append("datas", JSON.stringify(mappedRows));
              console.log('Mapped alert files:', mappedRows);

              const response = await fetch(serverAddress.current + "/UploadAlertImages", {
                  method: 'POST',
                  body: formData,
                  headers: {
                  'Content-Type': 'multipart/form-data',
                  },
              });
          
              if (!response.ok) {
                if (totalHours < 12) {
                  console.error(`Upload formData error, ${20 * Math.max(totalHours, 1)}秒重試`);
                  uploadTimeoutId.current = setTimeout(() => { setUploadTrigger(prev => prev + 1) }, 20000 * Math.max(totalHours, 1));
                  // console.log('uploadTimeoutId', uploadTimeoutId.current);
                  return;
                }
              } else {
                console.log('response', response);
                const responseData = await response.json();
                console.log(responseData);
                console.log('Images uploaded successfully!');
              }
      
              // Delete the files after upload
              for (const row of allRows) {
                  await FileSystem.deleteAsync(row.path);
                  console.log('delete', row.path);
              }
          }
        } 

        const result = await db.runAsync('DELETE FROM alerts WHERE id = ?', [alerts.id]);
        const result2 = await db.runAsync('DELETE FROM alertfiles WHERE alertId = ?', [alerts.id]);
        // console.log('result delete alerts', result, result2);
        // setLogs((prevLogs: LogEntry[]) => [
        //   ...prevLogs,
        //   {
        //     timestamp: Date.now(),
        //     message: 'uploadAlert_ 1秒後再上傳'
        //   }
        // ]);

        // 處理完畢，馬上檢查是否有下一筆資料
        uploadTimeoutId.current = setTimeout(() => { setUploadTrigger(prev => prev + 1) }, 1000);
        // console.log('uploadTimeoutId', uploadTimeoutId.current); // Access current value

    } catch (error) {
        // 全局錯誤處理，20秒後重試
        uploadInProcess.current = 0;
        console.warn('653 Upload alert error 15秒重試:');
        addLog('*Upload error 稍後重試-' + JSON.stringify(error));
        uploadTimeoutId.current = setTimeout(() => { setUploadTrigger(prev => prev + 1) }, 15000); // Assign to current
        console.log('uploadTimeoutId', uploadTimeoutId.current); // Access current value
      }
  };


  const endPatrol = async (prs: any) => {
    console.log('endPatrol prs', prs);
    if (!!endPatrolTimeoutId.current) {
        clearTimeout(endPatrolTimeoutId.current);
        endPatrolTimeoutId.current = null; // Reset to null
    }

    // get the time of 2 hours before prs.startTime
    const startTime = new Date(prs.StartTime);
    startTime.setHours(startTime.getHours() - 2);
    const endTime = new Date(prs.StartTime);
    console.log('endPatrol startTime', startTime);
    console.log('endPatrol   endTime', endTime);

    // get the alerts between startTime and endTime
    const sendprs = globalData.org.prs.filter((x: any) => {
        const xstartTime = new Date(x.StartTime);
        return !!x.CheckTime && x.Code == prs.Code && xstartTime >= startTime && xstartTime <= endTime;
    });
    console.log('endPatrol sendprs', sendprs.length);
    if (sendprs.length == 0) {
      console.log('endPatrol records is null');
      return;
    }

    const sendData = sendprs.map((x: any) => { return { 
        PrecId: x.PrecId, 
        CheckTime: x.CheckTime, 
        username: globalData.userInfo.username, 
        UserId: globalData.userInfo.UserId
      }; 
    });

    console.log('endPatrol sendData', sendData);

    let retObj = { 
        AlarmType: 0, 
        AlarmTime: '', 
        Latitude: 0, 
        Longitude: 0, 
        PrecId: null, 
        Photo: 0, 
        upload: 0, 
        trys: 0, 
        success: false, 
        message: ''
    };

    try {
      // retObj = await tryUpload(sendData, "EndPatrol");
      retObj = await upload(sendData, serverAddress.current + "/EndPatrol", settings.httpHeader, addLog, CONFIG);
      console.log('****上傳endPatrol retObj', retObj);
      if (retObj.success !== true) {
          if (retObj.message == "delete") {
              console.log('message == delete, 取消上傳');
          } else {
              console.warn('endPatrol error 60秒重試');
              endPatrolTimeoutId.current = setTimeout(() => endPatrol(prs), 60000);
              return;
          }
      }
    } catch (error) {
        console.warn('endPatrol error 60秒重試:', error);
        addLog('*endPatrol error 60秒重試');
        endPatrolTimeoutId.current = setTimeout(() => endPatrol(prs), 60000);
    }
  };

  // 定義一個函數來更新資料庫中的 userInfo 和 words
  const updateUserInfoInDb = async (userInfo: any, words: Record<string, string>) => {
    if (db) {
      try {
        await db.runAsync('UPDATE userInfo SET info=?, words=?', [
          JSON.stringify(userInfo),
          JSON.stringify(words),
        ]);
        console.log('userInfo and words updated in database');
      } catch (error) {
        console.error('Error updating userInfo and words in database:', error);
      }
    } else {
      console.warn('Database not initialized yet');
    }
  };

  // 定義一個函數來插入警報資料到資料庫
  const insertAlertToDb = async (alertData: any, photos: any[]): Promise<number> => {
    if (!db) {
      throw new Error('Database not initialized yet');
    }

    try {
      // === 將警報主要資訊存入資料庫 ===
      await db.runAsync(
        "INSERT INTO alerts (username,Guid,CountryId,FactoryId,PointId, UserId,CheckTime," +
        "AlarmType,AlarmTime,Latitude, Longitude,AlarmDesc,PrecId,Photo,upload,trys) VALUES (" +
        "?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?,?)",
        [
          alertData.username,        // 使用者名稱
          alertData.alertId,         // 警報唯一 ID
          alertData.countryId,       // 國家 ID
          alertData.factoryId,       // 工廠 ID
          alertData.pointId,         // 點位 ID
          alertData.userId,          // 使用者 ID
          alertData.checkTime,       // 檢查時間
          alertData.alarmType,       // 警報類型
          alertData.alarmTime,       // 警報時間
          alertData.latitude,        // 緯度
          alertData.longitude,       // 經度
          alertData.description,     // 警報描述
          alertData.precId,          // 巡邏記錄 ID
          photos.length,             // 照片數量
          0,                         // 上傳狀態（0 = 未上傳）
          0                          // 重試次數
        ]
      );

      // === 取得新插入的警報記錄 ID ===
      const result = await db.getFirstAsync<any>('SELECT last_insert_rowid() as id');
      const newId = result.id;
      console.log('新插入的警報記錄 ID:', newId);

      // === 儲存照片資訊到照片表 ===
      console.log('照片數:', photos.length);
      for (let i = 0; i < photos.length; i++) {
        console.log('照片檔:', photos[i].uri);
        await db.runAsync(
          "INSERT INTO alertfiles (alertId, path, PhotoId, time, Latitude, Longitude) VALUES " +
          "(?, ?, ?, ?, ?, ?)",
          [
            newId,                           // 警報記錄 ID
            photos[i].uri,                   // 照片檔案路徑
            photos[i].photoId,               // 照片唯一 ID
            alertData.alarmTime,             // 照片時間
            photos[i].latitude,              // 照片拍攝位置緯度
            photos[i].longitude              // 照片拍攝位置經度
          ]
        );
        const photoResult = await db.getFirstAsync<any>('SELECT last_insert_rowid() as id');
        console.log('新照片的記錄 ID:', photoResult.id);
      }

      addLog(`警報資料已儲存到資料庫，ID: ${newId}`);
      return newId;

    } catch (error) {
      console.error('Error inserting alert to database:', error);
      addLog(`警報資料儲存失敗: ${error}`);
      throw error;
    }
  };

  return (
    <AppContext.Provider value={{ 
      globalData, 
      setGlobalData, 
      uuidv4, 
      formatDateTime, 
      UploadAlert,
      endPatrol,
      setAlertQueue, 
      alertQueue,
      appMessage,
      setAppMessage,
      settings,
      setSettings,
      logs,
      setLogs,
      addLog,
      updateUserInfoInDb,
      insertAlertToDb,
      db
      }}>
      {children}



    </AppContext.Provider>
  );
}

// 自定義Hook，用於在元件中使用應用程式上下文
export function useAppContext() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
}

export default AppProvider;
export const CONFIG = {
  MAX_RETRIES: 2,
  NETWORK_CHECK_INTERVAL: 15000,
  UPLOAD_RETRY_DELAY: 15000,
  REQUEST_TIMEOUT: 10000,
  END_PATROL_RETRY_DELAY: 60000,
  MAX_LOG_ENTRIES: 100 // 限制日誌最大數量
} as const;
