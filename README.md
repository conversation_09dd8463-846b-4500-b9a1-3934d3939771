# 巡邏管理應用程式文件

## 概述
這是一個為工廠保全人員設計的行動巡邏管理應用程式。該應用程式幫助追蹤巡邏路線、管理警報，並使用NFC技術記錄檢查點。

## 主要功能
- 具有登入系統的使用者認證
- 巡邏路線追蹤與管理
- 警報回報功能
- NFC掃描點驗證
- 多語言支援(中文、英文、越南文等)
- 具有本地資料儲存的離線功能
- 測試與設定的除錯模式

## 技術架構
- 使用React Native和Expo建構
- 使用SQLite進行本地資料儲存
- REST API整合進行伺服器通訊
- 使用Context API進行全域狀態管理
- 支援Android平台

## 使用說明
1. 登入login: 輸入使用者名稱和密碼
2. 巡邏patrol: 選擇巡邏路線並掃描NFC點
3. 警報alarms: 回報安全事件
4. 變更密碼changepass: 變更密碼(在除錯模式中)
5. 變更伺服器changeUrl: 變更伺服器URL(在除錯模式中)

## 開發備註
- 快速點擊版本號5次可啟動除錯模式
- 在除錯模式中可設定API端點
- 可在除錯模式中查看日誌以進行故障排除


## Expo CLI 環境需求與安裝

### 環境需求
在開始開發之前，請確保您的系統已安裝以下軟體：

- **Node.js**: 建議使用 LTS (長期支援) 版本。您可以從 [Node.js 官方網站](https://nodejs.org/) 下載並安裝。

### 安裝 Expo CLI
Expo CLI 是一個命令列工具，用於建立、開發和發布 Expo 專案。請按照以下步驟安裝：

1. 開啟您的終端機或命令提示字元。
2. 執行以下命令全域安裝 Expo CLI：
   ```bash
   npm install -g expo-cli
   ```
3. 安裝完成後，您可以執行以下命令驗證安裝是否成功：
   ```bash
   expo --version
   ```
   這將顯示已安裝的 Expo CLI 版本號。

### 開發文件
Expo 開發文件請參考: https://docs.expo.dev/