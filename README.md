# 巡邏管理應用程式文件

## 概述
這是一個為工廠保全人員設計的行動巡邏管理應用程式。該應用程式幫助追蹤巡邏路線、管理警報，並使用NFC技術記錄檢查點。

## 主要功能
- 具有登入系統的使用者認證
- 巡邏路線追蹤與管理
- 警報回報功能
- NFC掃描點驗證
- 多語言支援(中文、英文、越南文等)
- 具有本地資料儲存的離線功能
- 測試與設定的除錯模式

## 技術架構

### 🏗️ **核心技術棧**
- **前端框架**: React Native + Expo SDK
- **程式語言**: TypeScript (型別安全)
- **狀態管理**: React Context API + useReducer
- **本地儲存**: SQLite (expo-sqlite)
- **網路通訊**: Fetch API + REST
- **導航系統**: Expo Router (檔案系統路由)

### 📱 **平台支援**
- **主要平台**: Android
- **開發環境**: Expo Development Build
- **部署方式**: APK 檔案分發

### 🔧 **關鍵技術特色**

#### **資料管理**
- **離線優先**: 本地 SQLite 資料庫儲存
- **自動同步**: 網路恢復時自動上傳資料
- **資料持久化**: 使用者設定、巡邏記錄、警報資料

#### **硬體整合**
- **NFC 掃描**: 使用 expo-nfc 進行 RFID 標籤讀取
- **相機功能**: expo-camera 拍照和條碼掃描
- **GPS 定位**: expo-location 位置服務
- **檔案系統**: expo-file-system 檔案管理

#### **網路與通訊**
- **AbortController**: 請求超時控制
- **重試機制**: 自動重試失敗的網路請求
- **網路狀態監控**: 即時網路連線檢查
- **批次上傳**: 離線資料批次同步

#### **安全性**
- **認證機制**: 使用者名稱/密碼登入
- **資料加密**: 敏感資料本地加密儲存
- **API 授權**: HTTP Header 授權驗證
- **版本控制**: 應用程式版本檢查和更新

### 🗂️ **資料庫架構**
```sql
-- 使用者資訊表
userInfo: {
  UserId, username, password,
  info (JSON), words (JSON), apiUrl
}

-- 警報佇列表
alertQueue: {
  id, alertData (JSON),
  timestamp, uploaded
}

-- 系統日誌表
logs: {
  id, message, timestamp, level
}
```

### 🌐 **API 整合**
- **登入端點**: `/AppLogin` - 使用者認證
- **警報上傳**: `/AppAlert` - 安全事件回報
- **巡邏結束**: `/AppEndPatrol` - 巡邏完成通知
- **檔案上傳**: 支援多媒體檔案上傳

## 應用程式架構說明

### app 資料夾結構與檔案用途

#### 🏠 **主要畫面檔案**
- **`index.tsx`** - 應用程式主頁面
  - 功能：應用程式入口點，管理登入狀態和主要導航
  - 特色：支援除錯模式（快速點擊版本號5次啟動）
  - 包含：使用者認證、功能按鈕、語言選擇、工廠選擇

- **`_layout.tsx`** - 應用程式佈局配置
  - 功能：定義全域佈局和導航結構
  - 管理：路由配置、主題設定、狀態列樣式

#### 🔐 **認證與設定檔案**
- **`login.tsx`** - 使用者登入認證元件
  - 功能：處理使用者登入、密碼驗證、伺服器通訊
  - 特色：支援重試機制、AbortController 超時控制
  - 包含：應用程式更新檢查、多語言支援、本地資料儲存

- **`selectFactory.tsx`** - 工廠選擇元件
  - 功能：讓使用者選擇工作的工廠或廠區
  - 用途：多廠區環境下的工廠切換功能

#### 🚶 **巡邏相關檔案**
- **`patrol.tsx`** - 主巡邏作業畫面
  - 功能：NFC 標籤掃描、巡邏點驗證、路線追蹤
  - 特色：即時位置記錄、巡邏進度顯示、下一個感應點提示
  - 包含：除錯掃描模式、巡邏完成處理、警報快速入口

- **`points.tsx`** - 巡邏點管理（管理員功能）
  - 功能：設定和管理巡邏點的 RFID 標籤
  - 用途：管理員配置巡邏路線和檢查點

#### 🚨 **警報系統檔案**
- **`alarm.tsx`** - 警報類型選擇畫面
  - 功能：選擇警報類型（火災、水災、入侵、停電等）
  - 用途：快速分類安全事件

- **`alarmSend.tsx`** - 警報發送元件
  - 功能：拍照記錄、位置資訊、警報內容輸入
  - 特色：支援多張照片、GPS 定位、離線儲存
  - 包含：相機整合、檔案管理、資料上傳

#### ⚙️ **系統管理檔案**
- **`changepass.tsx`** - 密碼變更功能
  - 功能：使用者密碼修改、驗證舊密碼
  - 安全：密碼強度檢查、確認密碼驗證

- **`changeUrl.tsx`** - 伺服器網址變更
  - 功能：修改 API 伺服器位址、QR Code 掃描設定
  - 特色：支援條碼掃描自動填入、網址格式驗證
  - 用途：除錯模式下的伺服器切換

- **`logs.tsx`** - 系統日誌檢視
  - 功能：顯示應用程式運行日誌、錯誤記錄
  - 用途：除錯和故障排除

#### 🧩 **輔助元件檔案**
- **`modal.tsx`** - 通用模組元件
  - 功能：提供可重用的彈出視窗元件
  - 用途：各種對話框和模組視窗

- **`+html.tsx`** - HTML 頁面配置
  - 功能：定義 Web 版本的 HTML 結構

- **`+not-found.tsx`** - 404 錯誤頁面
  - 功能：處理找不到頁面的情況

### 📁 **子資料夾說明**

#### `context/` - 全域狀態管理
- **`AppContext.tsx`** - 主要應用程式上下文
  - 功能：全域狀態管理、資料庫操作、API 通訊
  - 包含：使用者資訊、設定管理、警報佇列、日誌系統
  - 特色：自動網路狀態檢查、資料同步、錯誤處理

#### `hooks/` - 自訂 React Hooks
- **`useDatabase.ts`** - 資料庫操作 Hook
  - 功能：SQLite 資料庫初始化和操作
- **`useUpload.ts`** - 資料上傳 Hook
  - 功能：處理警報資料和檔案上傳

#### `components/` - 可重用元件
- **`ChangePassword.tsx`** - 密碼變更元件
- **`StatusBarWrapper.tsx`** - 狀態列包裝元件

## 使用說明
1. **登入 (login.tsx)**: 輸入使用者名稱和密碼進行身份驗證
2. **巡邏作業 (patrol.tsx)**: 選擇巡邏路線並使用 NFC 掃描感應點
3. **警報回報 (alarm.tsx + alarmSend.tsx)**: 選擇警報類型並拍照回報安全事件
4. **變更密碼 (changepass.tsx)**: 修改使用者密碼（一般使用者功能）
5. **巡邏點設定 (points.tsx)**: 設定巡邏點 RFID 標籤（管理員功能）
6. **變更伺服器 (changeUrl.tsx)**: 變更伺服器 URL（除錯模式）
7. **系統日誌 (logs.tsx)**: 查看系統運行記錄（除錯模式）

## 開發備註
- **除錯模式啟動**: 快速點擊版本號 5 次可啟動除錯模式
- **除錯功能**: 在除錯模式中可設定 API 端點、查看系統日誌
- **開發工具**: 支援模擬 NFC 掃描、網路狀態監控
- **故障排除**: 可在除錯模式中查看詳細日誌以進行故障排除


## 🚀 開發環境設定

### 📋 **環境需求**
在開始開發之前，請確保您的系統已安裝以下軟體：

- **Node.js**: 建議使用 LTS (長期支援) 版本 16.x 或更高
- **npm** 或 **yarn**: 套件管理工具
- **Android Studio**: Android 開發環境（用於模擬器和建置）
- **Git**: 版本控制系統

### ⚡ **快速開始**

#### 1. 安裝 Expo CLI
```bash
# 全域安裝 Expo CLI
npm install -g @expo/cli

# 驗證安裝
expo --version
```

#### 2. 專案設定
```bash
# 複製專案
git clone [repository-url]
cd patrol53b

# 安裝相依套件
npm install

# 啟動開發伺服器
expo start
```

#### 3. 開發模式運行
```bash
# 開發模式（含熱重載）
expo start --dev-client

# 清除快取重新啟動
expo start --clear

# 在 Android 模擬器中運行
expo start --android
```

### 🔧 **開發工具配置**

#### **VS Code 擴充套件建議**
- ES7+ React/Redux/React-Native snippets
- TypeScript Importer
- Expo Tools
- SQLite Viewer

#### **除錯工具**
- **Flipper**: React Native 除錯工具
- **React DevTools**: 元件檢查工具
- **Network Inspector**: 網路請求監控
- **SQLite Browser**: 資料庫檢視工具

### 📦 **建置與部署**

#### **開發建置**
```bash
# 建立開發建置
expo build:android

# 本地建置（需要 Android Studio）
expo run:android
```

#### **生產建置**
```bash
# 建立生產 APK
eas build --platform android --profile production

# 建立 AAB 檔案（Google Play）
eas build --platform android --profile production --format aab
```

#### **版本管理**
```bash
# 更新版本號
expo install expo-updates

# 發布 OTA 更新
eas update --branch production
```

### 🧪 **測試指南**

#### **單元測試**
```bash
# 安裝測試相依套件
npm install --save-dev jest @testing-library/react-native

# 執行測試
npm test
```

#### **整合測試**
- **NFC 功能**: 使用實體裝置測試 RFID 掃描
- **相機功能**: 測試拍照和條碼掃描
- **網路功能**: 測試離線/線上模式切換
- **資料庫**: 驗證 SQLite 資料持久化

### 📚 **開發資源**

#### **官方文件**
- [Expo 開發文件](https://docs.expo.dev/)
- [React Native 文件](https://reactnative.dev/docs/getting-started)
- [TypeScript 文件](https://www.typescriptlang.org/docs/)

#### **相關套件文件**
- [expo-sqlite](https://docs.expo.dev/versions/latest/sdk/sqlite/)
- [expo-camera](https://docs.expo.dev/versions/latest/sdk/camera/)
- [expo-nfc](https://docs.expo.dev/versions/latest/sdk/nfc/)
- [expo-location](https://docs.expo.dev/versions/latest/sdk/location/)

### 🐛 **常見問題排除**

#### **建置問題**
```bash
# 清除 node_modules 和重新安裝
rm -rf node_modules package-lock.json
npm install

# 清除 Expo 快取
expo start --clear

# 重設 Metro bundler
npx react-native start --reset-cache
```

#### **Android 相關問題**
- 確保 Android SDK 路徑正確設定
- 檢查 USB 除錯模式是否啟用
- 驗證裝置是否正確連接

#### **權限問題**
- 相機權限：在 app.json 中設定相機權限
- 位置權限：確保 GPS 權限已授予
- NFC 權限：檢查裝置 NFC 功能是否啟用

### 🔒 **安全性考量**

#### **API 金鑰管理**
- 使用環境變數儲存敏感資訊
- 不要將 API 金鑰提交到版本控制
- 使用 expo-constants 管理配置

#### **資料保護**
- 本地資料庫加密
- 網路傳輸使用 HTTPS
- 使用者密碼雜湊處理