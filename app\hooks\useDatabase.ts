import * as SQLite from 'expo-sqlite';
import { use, useEffect, useState } from 'react';

const DATABASE_NAME = 'patrol.db';

export function useDatabase(caller: string) {
  const [db, setDb] = useState<SQLite.SQLiteDatabase | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const openDatabase = async () => {
      try {
        const database = await SQLite.openDatabaseAsync(DATABASE_NAME);
        setDb(database);
        setIsLoading(false);
        console.log(caller + ' *** Database opened successfully!');
        //alert('Database opened successfully!');
        // Optional: Initialize your tables here
        await database.execAsync(`
          PRAGMA journal_mode = WAL;
          CREATE TABLE IF NOT EXISTS alerts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            Guid TEXT, PrecId TEXT, username TEXT,
            CountryId TEXT, FactoryId TEXT, PointId TEXT, UserId TEXT,
            CheckTime TEXT,
            AlarmType INTEGER, AlarmTime TEXT,
            Latitude REAL, Longitude REAL,
            AlarmDesc TEXT, Photo INTEGER,
            trys INTEGER, upload INTEGER
          );
          
          CREATE TABLE IF NOT EXISTS alertfiles (
            id INTEGER PRIMARY KEY,
            alertId INTEGER,
            path TEXT,
            PhotoId TEXT,
            time TEXT,
            Latitude REAL,
            Longitude REAL
          );
          
          CREATE TABLE IF NOT EXISTS userInfo (
            id INTEGER PRIMARY KEY,
            info TEXT,
            apiUrl TEXT,
            words TEXT
          );
        `);
        console.log('Tables initialized! from ' + caller);

      } catch (err) {
        console.error('Failed to open database or initialize tables:', err);
        setError(err as Error);
        setIsLoading(false);
      }
    };

    openDatabase();

    // No explicit close needed for expo-sqlite as it manages connections
    // but you could add cleanup if needed for other resources.
  }, []);

  return { db, isLoading, error };
}