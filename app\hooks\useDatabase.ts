import * as SQLite from 'expo-sqlite';
import { useEffect, useState } from 'react';

const DATABASE_NAME = 'patrol.db';

// 定義警報資料介面
export interface AlertData {
  username: string;
  alertId: string;
  countryId: string;
  factoryId: string;
  pointId: string;
  userId: string;
  checkTime: string;
  alarmType: number;
  alarmTime: string;
  latitude: number | null;
  longitude: number | null;
  description: string;
  precId: string;
}

// 定義照片資料介面
export interface PhotoData {
  uri: string;
  photoId: string;
  latitude: number | null;
  longitude: number | null;
}

// 定義警報查詢結果介面
export interface AlertRecord {
  id: number;
  upload: number;
  Guid: string;
  CheckTime: any;
  AlarmTime: any;
  PrecId: string;
  UserId: string;
  AlarmType: number;
}

// 定義照片檔案查詢結果介面
export interface AlertFileRecord {
  alertId: number;
  PhotoId: string;
  path: string;
  Latitude: number;
  Longitude: number;
  time: string;
}

export function useDatabase(caller: string) {
  const [db, setDb] = useState<SQLite.SQLiteDatabase | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const openDatabase = async () => {
      try {
        const database = await SQLite.openDatabaseAsync(DATABASE_NAME);
        setDb(database);
        setIsLoading(false);
        console.log(caller + ' *** Database opened successfully!');
        //alert('Database opened successfully!');
        // Optional: Initialize your tables here
        await database.execAsync(`
          PRAGMA journal_mode = WAL;
          CREATE TABLE IF NOT EXISTS alerts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            Guid TEXT, PrecId TEXT, username TEXT,
            CountryId TEXT, FactoryId TEXT, PointId TEXT, UserId TEXT,
            CheckTime TEXT,
            AlarmType INTEGER, AlarmTime TEXT,
            Latitude REAL, Longitude REAL,
            AlarmDesc TEXT, Photo INTEGER,
            trys INTEGER, upload INTEGER
          );
          
          CREATE TABLE IF NOT EXISTS alertfiles (
            id INTEGER PRIMARY KEY,
            alertId INTEGER,
            path TEXT,
            PhotoId TEXT,
            time TEXT,
            Latitude REAL,
            Longitude REAL
          );
          
          CREATE TABLE IF NOT EXISTS userInfo (
            id INTEGER PRIMARY KEY,
            info TEXT,
            apiUrl TEXT,
            words TEXT
          );
        `);
        console.log('Tables initialized! from ' + caller);

      } catch (err) {
        console.error('Failed to open database or initialize tables:', err);
        setError(err as Error);
        setIsLoading(false);
      }
    };

    openDatabase();

    // No explicit close needed for expo-sqlite as it manages connections
    // but you could add cleanup if needed for other resources.
  }, []);

  // === 資料庫操作函數 ===

  /**
   * 更新使用者資訊和多語言文字到資料庫
   */
  const updateUserInfoInDb = async (userInfo: any, words: Record<string, string>) => {
    if (!db) {
      throw new Error('Database not initialized yet');
    }

    try {
      await db.runAsync('UPDATE userInfo SET info=?, words=?', [
        JSON.stringify(userInfo),
        JSON.stringify(words),
      ]);
      console.log('userInfo and words updated in database');
    } catch (error) {
      console.error('Error updating userInfo and words in database:', error);
      throw error;
    }
  };

  /**
   * 更新 API URL 到資料庫
   */
  const updateApiUrlInDb = async (apiUrl: string) => {
    if (!db) {
      throw new Error('Database not initialized yet');
    }

    try {
      console.log('自動更新資料庫中的 API URL:', apiUrl);
      await db.runAsync('UPDATE userInfo SET apiUrl = ?', [apiUrl]);
      console.log('資料庫 API URL 更新完成');
    } catch (error) {
      console.error('更新資料庫 API URL 失敗:', error);
      throw error;
    }
  };

  /**
   * 插入警報資料到資料庫
   */
  const insertAlertToDb = async (alertData: AlertData, photos: PhotoData[]): Promise<number> => {
    if (!db) {
      throw new Error('Database not initialized yet');
    }

    try {
      // === 將警報主要資訊存入資料庫 ===
      await db.runAsync(
        "INSERT INTO alerts (username,Guid,CountryId,FactoryId,PointId, UserId,CheckTime," +
        "AlarmType,AlarmTime,Latitude, Longitude,AlarmDesc,PrecId,Photo,upload,trys) VALUES (" +
        "?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?,?)",
        [
          alertData.username,        // 使用者名稱
          alertData.alertId,         // 警報唯一 ID
          alertData.countryId,       // 國家 ID
          alertData.factoryId,       // 工廠 ID
          alertData.pointId,         // 點位 ID
          alertData.userId,          // 使用者 ID
          alertData.checkTime,       // 檢查時間
          alertData.alarmType,       // 警報類型
          alertData.alarmTime,       // 警報時間
          alertData.latitude,        // 緯度
          alertData.longitude,       // 經度
          alertData.description,     // 警報描述
          alertData.precId,          // 巡邏記錄 ID
          photos.length,             // 照片數量
          0,                         // 上傳狀態（0 = 未上傳）
          0                          // 重試次數
        ]
      );

      // === 取得新插入的警報記錄 ID ===
      const result = await db.getFirstAsync<any>('SELECT last_insert_rowid() as id');
      const newId = result.id;
      console.log('新插入的警報記錄 ID:', newId);

      // === 儲存照片資訊到照片表 ===
      console.log('照片數:', photos.length);
      for (let i = 0; i < photos.length; i++) {
        console.log('照片檔:', photos[i].uri);
        await db.runAsync(
          "INSERT INTO alertfiles (alertId, path, PhotoId, time, Latitude, Longitude) VALUES " +
          "(?, ?, ?, ?, ?, ?)",
          [
            newId,                           // 警報記錄 ID
            photos[i].uri,                   // 照片檔案路徑
            photos[i].photoId,               // 照片唯一 ID
            alertData.alarmTime,             // 照片時間
            photos[i].latitude,              // 照片拍攝位置緯度
            photos[i].longitude              // 照片拍攝位置經度
          ]
        );
        const photoResult = await db.getFirstAsync<any>('SELECT last_insert_rowid() as id');
        console.log('新照片的記錄 ID:', photoResult.id);
      }

      console.log(`警報資料已儲存到資料庫，ID: ${newId}`);
      return newId;

    } catch (error) {
      console.error('Error inserting alert to database:', error);
      throw error;
    }
  };

  /**
   * 查詢所有未上傳的警報記錄
   */
  const getAllUnuploadedAlerts = async (): Promise<AlertRecord[]> => {
    if (!db) {
      throw new Error('Database not initialized yet');
    }

    try {
      const allAlerts = await db.getAllAsync<AlertRecord>(
        'SELECT * FROM alerts ORDER BY id'
      );
      return allAlerts;
    } catch (error) {
      console.error('Error getting unuploaded alerts:', error);
      throw error;
    }
  };

  /**
   * 刪除警報記錄
   */
  const deleteAlert = async (alertId: number): Promise<void> => {
    if (!db) {
      throw new Error('Database not initialized yet');
    }

    try {
      await db.runAsync('DELETE FROM alerts WHERE id = ?', [alertId]);
      console.log(`警報記錄 ${alertId} 已刪除`);
    } catch (error) {
      console.error('Error deleting alert:', error);
      throw error;
    }
  };

  /**
   * 更新警報上傳狀態
   */
  const updateAlertUploadStatus = async (alertId: number, uploadStatus: number): Promise<void> => {
    if (!db) {
      throw new Error('Database not initialized yet');
    }

    try {
      await db.runAsync('UPDATE alerts SET upload = ? WHERE id = ?', [uploadStatus, alertId]);
      console.log(`警報記錄 ${alertId} 上傳狀態已更新為 ${uploadStatus}`);
    } catch (error) {
      console.error('Error updating alert upload status:', error);
      throw error;
    }
  };

  /**
   * 查詢警報相關的所有照片記錄
   */
  const getAlertFiles = async (alertId: number): Promise<AlertFileRecord[]> => {
    if (!db) {
      throw new Error('Database not initialized yet');
    }

    try {
      const allRows = await db.getAllAsync<AlertFileRecord>(
        'SELECT * FROM alertfiles WHERE alertId = ?',
        [alertId]
      );
      return allRows;
    } catch (error) {
      console.error('Error getting alert files:', error);
      throw error;
    }
  };

  /**
   * 刪除警報相關的所有照片記錄
   */
  const deleteAlertFiles = async (alertId: number): Promise<void> => {
    if (!db) {
      throw new Error('Database not initialized yet');
    }

    try {
      await db.runAsync('DELETE FROM alertfiles WHERE alertId = ?', [alertId]);
      console.log(`警報 ${alertId} 的照片記錄已刪除`);
    } catch (error) {
      console.error('Error deleting alert files:', error);
      throw error;
    }
  };

  return {
    db,
    isLoading,
    error,
    // 資料庫操作函數
    updateUserInfoInDb,
    updateApiUrlInDb,
    insertAlertToDb,
    getAllUnuploadedAlerts,
    deleteAlert,
    updateAlertUploadStatus,
    getAlertFiles,
    deleteAlertFiles
  };
}