import * as SQLite from 'expo-sqlite';
import { useEffect, useState } from 'react';

const DATABASE_NAME = 'patrol.db';

// 單例資料庫實例
let databaseInstance: SQLite.SQLiteDatabase | null = null;
let initializationPromise: Promise<SQLite.SQLiteDatabase> | null = null;

/**
 * 單例模式的資料庫初始化函數
 * 確保整個應用程式只有一個資料庫連接
 */
const initializeDatabase = async (caller: string): Promise<SQLite.SQLiteDatabase> => {
  // 如果已經有實例，直接返回
  if (databaseInstance) {
    console.log(`${caller} *** Using existing database connection`);
    return databaseInstance;
  }

  // 如果正在初始化，等待初始化完成
  if (initializationPromise) {
    console.log(`${caller} *** Waiting for database initialization`);
    return initializationPromise;
  }

  // 開始初始化
  console.log(`${caller} *** Starting database initialization`);
  initializationPromise = (async () => {
    try {
      const database = await SQLite.openDatabaseAsync(DATABASE_NAME);

      // 初始化表結構
      await database.execAsync(`
        PRAGMA journal_mode = WAL;
        CREATE TABLE IF NOT EXISTS alerts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          Guid TEXT, PrecId TEXT, username TEXT,
          CountryId TEXT, FactoryId TEXT, PointId TEXT, UserId TEXT,
          CheckTime TEXT,
          AlarmType INTEGER, AlarmTime TEXT,
          Latitude REAL, Longitude REAL,
          AlarmDesc TEXT, Photo INTEGER,
          trys INTEGER, upload INTEGER
        );

        CREATE TABLE IF NOT EXISTS alertfiles (
          id INTEGER PRIMARY KEY,
          alertId INTEGER,
          path TEXT,
          PhotoId TEXT,
          time TEXT,
          Latitude REAL,
          Longitude REAL
        );

        CREATE TABLE IF NOT EXISTS userInfo (
          id INTEGER PRIMARY KEY,
          info TEXT,
          apiUrl TEXT,
          words TEXT
        );
      `);

      databaseInstance = database;
      console.log(`${caller} *** Database initialized successfully!`);
      return database;

    } catch (error) {
      console.error(`${caller} *** Failed to initialize database:`, error);
      initializationPromise = null; // 重置，允許重試
      throw error;
    }
  })();

  return initializationPromise;
};

// 定義警報資料介面
export interface AlertData {
  username: string;
  alertId: string;
  countryId: string;
  factoryId: string;
  pointId: string;
  userId: string;
  checkTime: string;
  alarmType: number;
  alarmTime: string;
  latitude: number | null;
  longitude: number | null;
  description: string;
  precId: string;
}

// 定義照片資料介面
export interface PhotoData {
  uri: string;
  photoId: string;
  latitude: number | null;
  longitude: number | null;
}

// 定義警報查詢結果介面
export interface AlertRecord {
  id: number;
  upload: number;
  Guid: string;
  CheckTime: any;
  AlarmTime: any;
  PrecId: string;
  UserId: string;
  AlarmType: number;
}

// 定義照片檔案查詢結果介面
export interface AlertFileRecord {
  alertId: number;
  PhotoId: string;
  path: string;
  Latitude: number;
  Longitude: number;
  time: string;
}

export function useDatabase(caller: string) {
  const [db, setDb] = useState<SQLite.SQLiteDatabase | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const setupDatabase = async () => {
      console.log(`${caller} *** Setting up database`);
      try {
        const database = await initializeDatabase(caller);
        setDb(database);
        setIsLoading(false);
      } catch (err) {
        console.error(`${caller} *** Failed to setup database:`, err);
        setError(err as Error);
        setIsLoading(false);
      }
    };

    setupDatabase();
  }, [caller]);

  // === 資料庫操作函數 ===

  /**
   * 更新使用者資訊和多語言文字到資料庫
   * 使用 UPSERT 操作：如果記錄存在則更新，不存在則插入
   */
  const updateUserInfoInDb = async (userInfo: any, words: Record<string, string>) => {
    if (!db) {
      throw new Error('Database not initialized yet');
    }

    try {
      // 先檢查是否有記錄存在
      const existingRecord = await db.getFirstAsync('SELECT id FROM userInfo LIMIT 1');

      if (existingRecord) {
        // 記錄存在，執行更新
        await db.runAsync('UPDATE userInfo SET info=?, words=? WHERE id=?', [
          JSON.stringify(userInfo),
          JSON.stringify(words),
          (existingRecord as any).id
        ]);
        console.log('userInfo and words updated in database');
      } else {
        // 記錄不存在，執行插入
        await db.runAsync('INSERT INTO userInfo (info, words, apiUrl) VALUES (?, ?, ?)', [
          JSON.stringify(userInfo),
          JSON.stringify(words),
          '' // 預設空的 apiUrl
        ]);
        console.log('userInfo and words inserted into database');
      }
    } catch (error) {
      console.error('Error updating userInfo and words in database:', error);
      throw error;
    }
  };

  /**
   * 更新 API URL 到資料庫
   * 使用 UPSERT 操作：如果記錄存在則更新，不存在則插入
   */
  const updateApiUrlInDb = async (apiUrl: string) => {
    if (!db) {
      throw new Error('Database not initialized yet');
    }

    try {
      console.log('自動更新資料庫中的 API URL:', apiUrl);

      // 先檢查是否有記錄存在
      const existingRecord = await db.getFirstAsync('SELECT id FROM userInfo LIMIT 1');

      if (existingRecord) {
        // 記錄存在，執行更新
        await db.runAsync('UPDATE userInfo SET apiUrl = ? WHERE id = ?', [
          apiUrl,
          (existingRecord as any).id
        ]);
        console.log('資料庫 API URL 更新完成');
      } else {
        // 記錄不存在，執行插入
        await db.runAsync('INSERT INTO userInfo (info, words, apiUrl) VALUES (?, ?, ?)', [
          '{}', // 預設空的 userInfo
          '{}', // 預設空的 words
          apiUrl
        ]);
        console.log('資料庫 API URL 插入完成');
      }
    } catch (error) {
      console.error('更新資料庫 API URL 失敗:', error);
      throw error;
    }
  };

  /**
   * 插入警報資料到資料庫
   */
  const insertAlertToDb = async (alertData: AlertData, photos: PhotoData[]): Promise<number> => {
    if (!db) {
      throw new Error('Database not initialized yet');
    }

    try {
      // === 資料驗證和預處理 ===
      console.log('準備插入警報資料:', {
        username: alertData.username,
        alertId: alertData.alertId,
        countryId: alertData.countryId,
        factoryId: alertData.factoryId,
        pointId: alertData.pointId,
        userId: alertData.userId,
        checkTime: alertData.checkTime,
        alarmType: alertData.alarmType,
        alarmTime: alertData.alarmTime,
        latitude: alertData.latitude,
        longitude: alertData.longitude,
        description: alertData.description,
        precId: alertData.precId,
        photoCount: photos.length
      });

      // === 將警報主要資訊存入資料庫 ===
      await db.runAsync(
        "INSERT INTO alerts (username,Guid,CountryId,FactoryId,PointId, UserId,CheckTime," +
        "AlarmType,AlarmTime,Latitude, Longitude,AlarmDesc,PrecId,Photo,upload,trys) VALUES (" +
        "?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?,?)",
        [
          alertData.username || '',          // 使用者名稱（確保不為 null）
          alertData.alertId || '',           // 警報唯一 ID（確保不為 null）
          alertData.countryId || '',         // 國家 ID（確保不為 null）
          alertData.factoryId || '',         // 工廠 ID（確保不為 null）
          alertData.pointId || '',           // 點位 ID（確保不為 null）
          alertData.userId || '',            // 使用者 ID（確保不為 null）
          alertData.checkTime || '',         // 檢查時間（確保不為 null）
          alertData.alarmType || 0,          // 警報類型（確保不為 null）
          alertData.alarmTime || '',         // 警報時間（確保不為 null）
          alertData.latitude || 0,           // 緯度（null 轉為 0）
          alertData.longitude || 0,          // 經度（null 轉為 0）
          alertData.description || '',       // 警報描述（確保不為 null）
          alertData.precId || '',            // 巡邏記錄 ID（確保不為 null）
          photos.length || 0,                // 照片數量（確保不為 null）
          0,                                 // 上傳狀態（0 = 未上傳）
          0                                  // 重試次數
        ]
      );

      // === 取得新插入的警報記錄 ID ===
      const result = await db.getFirstAsync<any>('SELECT last_insert_rowid() as id');
      const newId = result.id;
      console.log('新插入的警報記錄 ID:', newId);

      // === 儲存照片資訊到照片表 ===
      console.log('照片數:', photos.length);
      for (let i = 0; i < photos.length; i++) {
        console.log('照片檔:', photos[i].uri);
        await db.runAsync(
          "INSERT INTO alertfiles (alertId, path, PhotoId, time, Latitude, Longitude) VALUES " +
          "(?, ?, ?, ?, ?, ?)",
          [
            newId,                                    // 警報記錄 ID
            photos[i].uri || '',                      // 照片檔案路徑（確保不為 null）
            photos[i].photoId || '',                  // 照片唯一 ID（確保不為 null）
            alertData.alarmTime || '',                // 照片時間（確保不為 null）
            photos[i].latitude || 0,                  // 照片拍攝位置緯度（null 轉為 0）
            photos[i].longitude || 0                  // 照片拍攝位置經度（null 轉為 0）
          ]
        );
        const photoResult = await db.getFirstAsync<any>('SELECT last_insert_rowid() as id');
        console.log('新照片的記錄 ID:', photoResult.id);
      }

      console.log(`警報資料已儲存到資料庫，ID: ${newId}`);
      return newId;

    } catch (error) {
      console.error('Error inserting alert to database:', error);
      console.error('Failed alertData:', JSON.stringify(alertData, null, 2));
      console.error('Failed photos:', JSON.stringify(photos, null, 2));
      console.error('Database state:', db ? 'Connected' : 'Not connected');
      throw error;
    }
  };

  /**
   * 查詢所有未上傳的警報記錄
   */
  const getAllUnuploadedAlerts = async (): Promise<AlertRecord[]> => {
    if (!db) {
      throw new Error('Database not initialized yet');
    }

    try {
      const allAlerts = await db.getAllAsync<AlertRecord>(
        'SELECT * FROM alerts ORDER BY id'
      );
      return allAlerts;
    } catch (error) {
      console.error('Error getting unuploaded alerts:', error);
      throw error;
    }
  };

  /**
   * 刪除警報記錄
   */
  const deleteAlert = async (alertId: number): Promise<void> => {
    if (!db) {
      throw new Error('Database not initialized yet');
    }

    try {
      await db.runAsync('DELETE FROM alerts WHERE id = ?', [alertId]);
      console.log(`警報記錄 ${alertId} 已刪除`);
    } catch (error) {
      console.error('Error deleting alert:', error);
      throw error;
    }
  };

  /**
   * 更新警報上傳狀態
   */
  const updateAlertUploadStatus = async (alertId: number, uploadStatus: number): Promise<void> => {
    if (!db) {
      throw new Error('Database not initialized yet');
    }

    try {
      await db.runAsync('UPDATE alerts SET upload = ? WHERE id = ?', [uploadStatus, alertId]);
      console.log(`警報記錄 ${alertId} 上傳狀態已更新為 ${uploadStatus}`);
    } catch (error) {
      console.error('Error updating alert upload status:', error);
      throw error;
    }
  };

  /**
   * 查詢警報相關的所有照片記錄
   */
  const getAlertFiles = async (alertId: number): Promise<AlertFileRecord[]> => {
    if (!db) {
      throw new Error('Database not initialized yet');
    }

    try {
      const allRows = await db.getAllAsync<AlertFileRecord>(
        'SELECT * FROM alertfiles WHERE alertId = ?',
        [alertId]
      );
      return allRows;
    } catch (error) {
      console.error('Error getting alert files:', error);
      throw error;
    }
  };

  /**
   * 刪除警報相關的所有照片記錄
   */
  const deleteAlertFiles = async (alertId: number): Promise<void> => {
    if (!db) {
      throw new Error('Database not initialized yet');
    }

    try {
      await db.runAsync('DELETE FROM alertfiles WHERE alertId = ?', [alertId]);
      console.log(`警報 ${alertId} 的照片記錄已刪除`);
    } catch (error) {
      console.error('Error deleting alert files:', error);
      throw error;
    }
  };

  /**
   * 等待資料庫初始化完成
   */
  const waitForDatabase = async (): Promise<SQLite.SQLiteDatabase> => {
    if (db) {
      return db;
    }

    // 等待資料庫初始化完成
    return new Promise((resolve, reject) => {
      const checkDatabase = () => {
        if (db) {
          resolve(db);
        } else if (error) {
          reject(error);
        } else {
          // 繼續等待
          console.log('等待資料庫初始化完成...');
          setTimeout(checkDatabase, 100);
        }
      };
      checkDatabase();
    });
  };

  return {
    db,
    isLoading,
    error,
    waitForDatabase,
    // 資料庫操作函數
    updateUserInfoInDb,
    updateApiUrlInDb,
    insertAlertToDb,
    getAllUnuploadedAlerts,
    deleteAlert,
    updateAlertUploadStatus,
    getAlertFiles,
    deleteAlertFiles
  };
}