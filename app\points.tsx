import React, { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import { View, Text, StyleSheet, Switch, TouchableOpacity, Alert } from 'react-native';
// import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useAppContext } from './context/AppContext';
import { Picker } from '@react-native-picker/picker';
import NfcManager, { NfcTech, NfcEvents } from 'react-native-nfc-manager';
import * as Location from 'expo-location';

const PointScreen = () => {

  const { globalData, settings } = useAppContext();
  const [locationSwitch, setLocationSwitch] = useState(false);
  const locationSwitchRef = useRef(locationSwitch);
  const [scanLocation, setScanLocation] = useState(false);
  
  console.log('render PointScreen...');  // 追蹤每次渲染
  console.log('uploadTag', settings.apiUrl + "/PostPointTag");

  // 追蹤 address 的變化
  const [address, setAddress] = useState({ 
    CountryId: '', RegionId: '', FactoryId: '', PointId: '', TagId: '', 
    DeviceId: '', Latitude: 0, Longitude: 0
  });

  // 移除多餘的過濾操作，改用 useMemo
  const filteredRegions = useMemo(() => 
    globalData.org.regions.filter(
      (region: { CountryId: string }) => region.CountryId === address.CountryId
    ),
    [address.CountryId, globalData.org.regions]
  );

  const filteredFactories = useMemo(() => 
    globalData.org.factories.filter(
      (factory: { RegionId: string }) => factory.RegionId === address.RegionId
    ),
    [address.RegionId, globalData.org.factories]
  );

  const filteredPoints = useMemo(() => 
    globalData.org.points.filter(
      (point: { FactoryId: string }) => point.FactoryId === address.FactoryId
    ),
    [address.FactoryId, globalData.org.points]
  );

  // 簡化 CountryId 變更的處理
  const handleCountryChange = useCallback((itemValue: string) => {
    const newCountryId = itemValue === "_empty_" ? "" : itemValue;
    setAddress(prev => ({
      ...prev,
      CountryId: newCountryId,
      RegionId: "",
      FactoryId: "",
      PointId: ""
    }));
  }, []);


  useEffect(() => {
    const initNfc = async () => {
      try {
        await NfcManager.start();
        
        // 設置監聽器來持續檢測NFC標籤
        NfcManager.setEventListener(NfcEvents.DiscoverTag, async (tag: any) => {
        //   alert('setEventListener:' + JSON.stringify(tag));
          let tagResult = '';
          if (!!tag && !!tag.id) {
            setAddress(prev => ({...prev, TagId: tag.id}));
            tagResult = tag.id;
          } else {
            const tagData = await NfcManager.getTag();
            // alert('tagData:' + JSON.stringify(tagData));
            if (tagData && tagData.id) {
              setAddress(prev => ({...prev, TagId: tagResult}));
              tagResult = tagData.id;
            }    
          }
        //   alert('tagResult' + tagResult);
        //   alert('locationSwitch' + locationSwitch);
          if (!!tagResult && locationSwitchRef.current == true) {
            const { status } = await Location.requestForegroundPermissionsAsync();
            // alert('取得位置******* ' + status);
            if (status === 'granted') {
              setScanLocation(true);
              let scanLocation = await Location.getCurrentPositionAsync({
                  accuracy: Location.Accuracy.Low,
                  timeInterval: 5000,  // 5秒的時間間隔
                  distanceInterval: 10 // 10公尺的距離間隔
              });
              setScanLocation(false);
              setAddress((prev) => ({...prev, TagId: tagResult, Latitude: scanLocation.coords.latitude, Longitude: scanLocation.coords.longitude}));
              alert('scanLocation' + JSON.stringify(scanLocation));
            } else {
              setAddress(prev => ({...prev, TagId: tagResult}));
            }
          } else if (!!tagResult) {
            setAddress(prev => ({...prev, TagId: tagResult}));
          }
      });

        // 開始監聽 NFC 標籤
        await NfcManager.registerTagEvent();
      } catch (ex) {
        console.warn('NFC初始化錯誤:', ex);
        // setAddress(prev => ({...prev, TagId: 'NFC not enabled'}));
        setAddress(prev => ({...prev, TagId: '12345678'}));
        setScanLocation(false);
      }
    };

    initNfc();
    setAddress(prev => ({...prev, TagId: 'scanning...'}));

    // 清理函數
    return () => {
      const cleanUp = async () => {
        try {
          NfcManager.setEventListener(NfcEvents.DiscoverTag, null);
          await NfcManager.unregisterTagEvent();
          // await NfcManager.cancelTechnologyRequest();
        } catch (error) {
          console.log('清理NFC時發生錯誤:', error);
        }
      };
      cleanUp();
    };
  }, []);


  // 當國家改變時，一次性更新所有相關欄位
  useEffect(() => {
    if (address.CountryId) {
      setAddress(prev => ({
        ...prev,
        RegionId: "",
        FactoryId: "",
        PointId: ""
      }));
    }
  }, [address.CountryId]);

  // 當區域改變時，一次性更新相關欄位
  useEffect(() => {
    if (address.RegionId) {
      setAddress(prev => ({
        ...prev,
        FactoryId: "",
        PointId: ""
      }));
    }
  }, [address.RegionId]);

  const uploadTag = async () => {
    console.log('uploadTag', settings.apiUrl + "/PostPointTag");
    let retObj = await tryUpload();
    console.log('retObj', retObj);
    if (retObj.success == true) {
        Alert.alert(
            '',
            globalData._words.success,
            [{ text: globalData._words.confirm || '確定' }]
          );
      setAddress(prev => ({...prev, TagId: ""})); 
    } else {
        alert("Upload failed");
    }
  }

  const tryUpload = async () => {
    let retryCount = 0;
    const maxRetries = 3;
    let alertData = {...address};
    while (retryCount < maxRetries) {
        try {
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('請求超時')), 10000);
            });

            console.log('settings.apiUrl', settings.apiUrl + "/PostPointTag");
            const fetchPromise = fetch(settings.apiUrl + "/PostPointTag", {
                method: 'POST',
                headers: settings.httpHeader,
                body: JSON.stringify(alertData),
            });


            const response1 = await Promise.race([fetchPromise, timeoutPromise]) as Response;
            if (!response1.ok) {
                throw new Error('upload failed');
            }
            return await response1.json();
        } catch (error) {
            retryCount++;
            if (retryCount === maxRetries) {
                throw error;
            }
            console.log(`upload失敗，第 ${retryCount} 次重試`);
            console.log('error', error);
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }
};

return (
    <View style={styles.container}>
      <View style={styles.pickerContainer}>
        <Text style={styles.label}>國家</Text>
        <Picker
          style={styles.picker}
          selectedValue={address.CountryId || "_empty_"}
          onValueChange={handleCountryChange}>
          <Picker.Item label="請選擇國家" value="_empty_" style={styles.pickerItem} />
          {globalData.org.countries
            .sort((a: { CountryName: string }, b: { CountryName: string }) => a.CountryName.localeCompare(b.CountryName))
            .map((country: { CountryId: string; CountryName: string }) => (
              <Picker.Item 
                key={country.CountryId} 
                label={country.CountryName} 
                value={country.CountryId} 
                style={styles.pickerItem}
              />
            ))}
        </Picker>

        <Text style={styles.label}>區域</Text>
        <Picker
          style={styles.picker}
          selectedValue={address.RegionId || "_empty_"}
          enabled={!!address.CountryId}
          onValueChange={(itemValue: string) => setAddress(prev => ({...prev, RegionId: itemValue === "_empty_" ? "" : itemValue}))}>
          <Picker.Item label="請選擇區域" value="_empty_" style={styles.pickerItem} />
          {filteredRegions
            .sort((a: { RegionName: string }, b: { RegionName: string }) => a.RegionName.localeCompare(b.RegionName))
            .map((region: { RegionId: string; RegionName: string }) => (
              <Picker.Item 
                key={region.RegionId} 
                label={region.RegionName} 
                value={region.RegionId} 
                style={styles.pickerItem}
              />
            ))}
        </Picker>

        <Text style={styles.label}>工廠</Text>
        <Picker
          style={styles.picker}
          selectedValue={address.FactoryId || "_empty_"}
          enabled={!!address.RegionId}
          onValueChange={(itemValue: string) => setAddress(prev => ({...prev, FactoryId: itemValue === "_empty_" ? "" : itemValue}))}>
          <Picker.Item label="請選擇工廠" value="_empty_" style={styles.pickerItem} />
          {filteredFactories
            .sort((a: { FactoryName: string }, b: { FactoryName: string }) => a.FactoryName.localeCompare(b.FactoryName))
            .map((factory: { FactoryId: string; FactoryName: string }) => (
              <Picker.Item 
                key={factory.FactoryId} 
                label={factory.FactoryName}
                value={factory.FactoryId}
                style={styles.pickerItem}
              />
            ))}
        </Picker>

        <Text style={styles.label}>巡邏點</Text>
        <Picker
          style={styles.picker}
          selectedValue={address.PointId || "_empty_"}
          enabled={!!address.FactoryId}
          onValueChange={(itemValue: string) => {
            // setSelectedPoint(itemValue === "_empty_" ? "" : itemValue);
            setAddress(prev => ({...prev, PointId: itemValue === "_empty_" ? "" : itemValue}));
          }}>
          <Picker.Item label="請選擇巡邏點" value="_empty_" style={styles.pickerItem} />
          {filteredPoints
            .sort((a: { CheckOrder: number }, b: { CheckOrder: number }) => a.CheckOrder - b.CheckOrder)
            .map((point: { PointId: string; PointName: string }) => (
              <Picker.Item 
                key={point.PointId} 
                label={point.PointName} 
                value={point.PointId}
                style={styles.pickerItem}
              />
            ))}
        </Picker>
        <View style={{marginTop: 20, flexDirection: 'row', justifyContent: 'flex-start'}}>
          <Text>標籤 ID : </Text>
          <Text style={{color: 'red'}}>{address.TagId}</Text>
        </View>
        <View style={styles.switchContainer}>
            <Text>GPS LOCATION：</Text>
            <Switch
              value={locationSwitch}
              onValueChange={(value) => {locationSwitchRef.current = value; setLocationSwitch(value);}}
            />
          </View>
        <View style={{marginTop: 20, alignItems: 'center'}}>
          <TouchableOpacity 
              style={[
                styles.touchButton,
                styles.sendButton,
                (!address.TagId || !address.PointId) && styles.sendButtonDisabled
              ]} 
              disabled={!address.TagId || !address.PointId || scanLocation}
              onPress={() => { uploadTag(); }}
            >
              <Text style={styles.buttonText}>{globalData._words.confirmAlertBtn}</Text>
            </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 11,
  },
  pickerContainer: {
    width: '100%',
  },
  label: {
    fontSize: 14,
    marginBottom: 4,
    color: '#333',
  },
  picker: {
    backgroundColor: '#a5f5f5',
    marginBottom: 4,
    borderRadius: 4,
  },
  pickerItem: {
    fontSize: 14,
    height: 38,
    padding: 0,
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
  },
  touchButton: {
    width: '60%',
    padding: 10,
    paddingTop: 4,
    borderRadius: 5,
    marginTop: 10,
    alignItems: 'center',
  },
  sendButton: {
    backgroundColor: '#4CAF50',
  },
  sendButtonDisabled: {
    backgroundColor: '#A5D6A7',  // 使用較淺的綠色
    opacity: 0.6,
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
  },
});

export default PointScreen;
