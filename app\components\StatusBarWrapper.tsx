import { StatusBar } from 'expo-status-bar';
import { View } from 'react-native';
import Constants from 'expo-constants';

interface StatusBarWrapperProps {
  backgroundColor?: string;
  statusBarStyle?: 'light' | 'dark' | 'auto';
}

export default function StatusBarWrapper({
  backgroundColor = '#111',
  statusBarStyle = 'light'
}: StatusBarWrapperProps) {
  const statusBarHeight = Constants.statusBarHeight;
  
  return (
    <View style={{width: '100%', height: statusBarHeight, backgroundColor}}>
      <StatusBar style={statusBarStyle} />
    </View>
  );
}