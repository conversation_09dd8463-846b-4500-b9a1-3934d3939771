import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useAppContext } from './context/AppContext';
import { Picker } from '@react-native-picker/picker';

type SelectFactoryProps = {
    isVisible: boolean;
    onClose: () => void;
    onSelect: (factoryId: string) => void;
  };
  
export default function SelectFactory({ isVisible, onClose, onSelect }: SelectFactoryProps) {
    const navigation = useNavigation();
  const { globalData } = useAppContext();
  
  const [selectedCountry, setSelectedCountry] = useState('');
  const [selectedRegion, setSelectedRegion] = useState('');
  const [selectedFactory, setSelectedFactory] = useState('');

//   console.log("points", globalData.org.points);

  // 根據選擇的國家過濾區域
  const filteredRegions = globalData.org.regions.filter(
    (region: { CountryId: string }) => region.CountryId === selectedCountry
  );

  // 根據選擇的區域過濾工廠
  const filteredFactories = globalData.org.factories.filter(
    (factory: { RegionId: string }) => factory.RegionId === selectedRegion
  );

  // 當國家改變時，重設區域和工廠
  useEffect(() => {
    setSelectedFactory('');
    let fitRegion = filteredRegions.filter((region: any) => region.CountryId === selectedCountry);
    if (fitRegion.length == 1) {
      setSelectedRegion(fitRegion[0].RegionId);
    } else {
      setSelectedRegion('');
    }
}, [selectedCountry]);

  // 當區域改變時，重設工廠
  useEffect(() => {
    let fitFactory = globalData.org.factories.filter((fac: any) => fac.RegionId === selectedRegion);
    if (fitFactory.length == 1) {
      setSelectedFactory(fitFactory[0].FactoryId);
    } else {
      setSelectedFactory('');
    }
}, [selectedRegion]);

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
    >
    <View style={styles.container}>
      <View style={styles.pickerContainer}>
        <Text style={styles.label}>國家</Text>
        <Picker
          style={styles.picker}
          selectedValue={selectedCountry}
          onValueChange={(itemValue: string) => setSelectedCountry(itemValue)}>
          <Picker.Item label="請選擇國家" value="" style={styles.pickerItem} />
          {globalData.org.countries
            .sort((a: { CountryName: string }, b: { CountryName: string }) => a.CountryName.localeCompare(b.CountryName))
            .map((country: { CountryId: string; CountryName: string }) => (
              <Picker.Item 
                key={country.CountryId} 
                label={country.CountryName} 
                value={country.CountryId} 
                style={styles.pickerItem}
              />
            ))}
        </Picker>

        <Text style={styles.label}>區域</Text>
        <Picker
          style={styles.picker}
          selectedValue={selectedRegion}
          enabled={!!selectedCountry}
          onValueChange={(itemValue: string) => setSelectedRegion(itemValue)}>
          <Picker.Item label="請選擇區域" value="" style={styles.pickerItem} />
          {filteredRegions
            .sort((a: { RegionName: string }, b: { RegionName: string }) => a.RegionName.localeCompare(b.RegionName))
            .map((region: { RegionId: string; RegionName: string }) => (
              <Picker.Item 
                key={region.RegionId} 
                label={region.RegionName} 
                value={region.RegionId} 
                style={styles.pickerItem}
              />
            ))}
        </Picker>

        <Text style={styles.label}>工廠</Text>
        <Picker
          style={styles.picker}
          selectedValue={selectedFactory}
          enabled={!!selectedRegion}
          onValueChange={(itemValue: string) => setSelectedFactory(itemValue)}>
          <Picker.Item label="請選擇工廠" value="" style={styles.pickerItem} />
          {filteredFactories
            .sort((a: { FactoryName: string }, b: { FactoryName: string }) => a.FactoryName.localeCompare(b.FactoryName))
            .map((factory: { FactoryId: string; FactoryName: string }) => (
              <Picker.Item 
                key={factory.FactoryId} 
                label={factory.FactoryName}
                value={factory.FactoryId}
                style={styles.pickerItem}
              />
            ))}
        </Picker>
        
            <TouchableOpacity
              style={[styles.factoryButton, {backgroundColor: selectedFactory ? '#cdeb87' : '#ccc'}]}
              onPress={() => {
                onSelect(selectedFactory);
                onClose();
              }}
              disabled={!selectedFactory}
            >
              <Text style={[styles.factoryButtonText, {color: selectedFactory ? '#333' : '#666'}]}>{globalData._words.confirm}</Text>
            </TouchableOpacity>

          <TouchableOpacity
            style={[styles.factoryButton, styles.cancelButton]}
            onPress={onClose}
          >
            <Text style={styles.cancelButtonText}>{globalData._words.cancel}</Text>
          </TouchableOpacity>

      </View>
    </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 11,
    backgroundColor: '#ddd',
  },
  pickerContainer: {
    width: '100%',
  },
  label: {
    fontSize: 14,
    marginBottom: 4,
    color: '#333',
  },
  picker: {
    backgroundColor: '#a5f5f5',
    marginBottom: 8,
    borderRadius: 4,
  },
  pickerItem: {
    fontSize: 14,
    height: 40,
    padding: 0,
  },
  factoryButton: {
    backgroundColor: '#cdeb87',
    padding: 10,
    borderRadius: 8,
    width: '100%',
    marginTop: 25,
  },
  factoryButtonText: {
    color: '#333',
    textAlign: 'center',
    fontSize: 16,
  },
  cancelButton: {
    backgroundColor: '#cdeb87',
    marginTop: 25,
  },
  cancelButtonText: {
    color: '#333',
    textAlign: 'center',
    fontSize: 16,
  },
});

