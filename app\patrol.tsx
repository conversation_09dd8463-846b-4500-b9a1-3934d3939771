// React 核心函式庫和 Hooks
import React, { useEffect, useState, useRef } from 'react';
// React Native 基礎 UI 元件
import { View, Text, StyleSheet, Image, TouchableOpacity, ScrollView } from 'react-native';
// 導航相關函式庫
import { useNavigation } from '@react-navigation/native';
// Material Design 圖示元件
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
// 應用程式全域狀態管理
import { useAppContext } from './context/AppContext';
// 資料庫操作 Hook
import { useDatabase, AlertData, PhotoData } from './hooks/useDatabase';
// Expo 位置服務 API
import * as Location from 'expo-location';
// NFC 管理器，用於 NFC 標籤讀取
import NfcManager, { NfcTech, NfcEvents } from 'react-native-nfc-manager';
// Expo Router 路由管理
import { router } from 'expo-router';

/**
 * PatrolScreen - 主巡邏畫面元件
 *
 * 功能說明：
 * 1. 顯示當前班次的巡邏路線和感應點狀態
 * 2. 處理 NFC 標籤掃描和驗證
 * 3. 記錄巡邏員的感應時間和位置資訊
 * 4. 管理巡邏進度和下一個感應點提示
 * 5. 提供警報功能的快速入口
 */
const PatrolScreen = () => {
  // 導航控制器
  const navigation = useNavigation();

  // 從全域狀態取得必要的函式和資料
  const {
    globalData,      // 全域資料（使用者資訊、組織資料等）
    formatDateTime,  // 日期時間格式化函式
    appMessage,      // 應用程式狀態訊息
    setAppMessage,   // 設定應用程式狀態訊息
    UploadAlert,     // 上傳警報資料函式
    endPatrol,       // 結束巡邏函式
    addLog           // 新增日誌函式
  } = useAppContext();

  // 從資料庫 Hook 取得資料庫操作函式
  const { insertAlertToDb } = useDatabase('patrol');

  // 狀態管理
  const [timeRange, setTimeRange] = useState('');                    // 班次時間範圍顯示
  const shiftPrs = useRef<any[]>([]);                               // 當前班次所有巡邏點參考
  const myprsRef = useRef<any[]>([]);                               // 當前使用者巡邏點參考（用於 NFC 回調）
  const [myprs, setMyprs] = useState<any[]>([]);                    // 當前使用者的巡邏點列表
  const [nextPoint, setNextPoint] = useState<any>({Rfid: null});    // 下一個要感應的點
  const [rangeTrigger, setRangeTrigger] = useState('');             // 時間範圍變更觸發器
  const [message, setMessage] = useState('');                       // 使用者操作回饋訊息

  // NFC 掃描相關狀態
  const [isScanning, setIsScanning] = useState(false);              // 是否正在掃描中
  const [canScan, setCanScan] = useState(false);                    // 是否可以進行掃描
  const isScanningRef = useRef(isScanning);                         // 掃描狀態參考（用於 NFC 事件回調）

  // 組織資料快捷參考
  let org = globalData.org;

  // 計時器參考
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 警報圖示輪播相關
  const [currentImageIndex, setCurrentImageIndex] = useState(0);    // 當前顯示的警報圖示索引

  // 警報類型圖示陣列
  const alarmImages = [
    require('../assets/images/fire.png'),      // 火災
    require('../assets/images/flood.png'),     // 水災
    require('../assets/images/intruder.png'),  // 入侵
    require('../assets/images/lobat.png'),     // 低電量
    require('../assets/images/electric.png')   // 電力異常
  ];

  /**
   * 計算並格式化時間範圍字串
   * 將班次的開始時間和結束時間格式化為 "HH:MM - HH:MM" 的顯示格式
   * @param e - 包含 StartTime 和 EndTime 屬性的班次物件
   * @returns 格式化後的時間範圍字串，如 "08:00 - 16:00"
   */
  const getRangeTime = (e: any) => {
    if (!!e.StartTime && !!e.EndTime) {
      return formatDateTime(new Date(e.StartTime)).substring(11, 16) + ' - ' +
                      formatDateTime(new Date(e.EndTime)).substring(11, 16);
    }
    return '';
  }

  /**
   * 元件初始化 useEffect
   * 負責以下初始化工作：
   * 1. 設定定時器定期檢查班次變更
   * 2. 根據開發/生產環境設定不同的應用程式狀態
   * 3. 初始化 NFC 功能和事件監聽
   * 4. 清理資源（在元件卸載時）
   */
  useEffect(() => {
    // 立即檢查當前班次
    getShiftPrs();

    // 設定定時器每分鐘檢查班次變更
    const timer = setInterval(() => {
      getShiftPrs();
    }, 60000); // 每分鐘檢查一次

    // 根據環境設定應用程式狀態訊息
    if (__DEV__) {
      setAppMessage('debug');
      console.log('App is running in debug mode');
    } else {
      setAppMessage('scanning');
      console.log('App is running in production mode');
    }

    /**
     * 初始化 NFC 功能
     * 設定 NFC 標籤檢測事件監聽器
     */
    const initNfc = async () => {
      try {
        // 啟動 NFC 管理器
        await NfcManager.start();

        addLog('NfcManager.setEventListener');

        // 設置 NFC 標籤發現事件監聽器
        NfcManager.setEventListener(NfcEvents.DiscoverTag, async (tag: any) => {
          // 防止重複掃描
          if (isScanningRef.current) return;

          // 檢查標籤是否有有效的 ID
          if (!!tag && !!tag.id) {
            addLog('NfcManager.startScan');
            startScan(tag.id);
          } else {
            // 嘗試手動取得標籤資料
            const tagData = await NfcManager.getTag();
            if (tagData && tagData.id) {
              // 預留處理邏輯
            }
          }
        });

        // 註冊 NFC 標籤事件監聽
        await NfcManager.registerTagEvent();

      } catch (ex) {
        console.warn('NFC初始化錯誤:', ex);
        addLog('NFC初始化錯誤');
        // 錯誤處理：可選擇延遲重試
        // setTimeout(() => {
        //   startScan('');
        // }, 5000);
      }
    };

    // 執行 NFC 初始化
    initNfc();

    /**
     * 清理函式：在元件卸載時執行
     * 清理定時器和 NFC 資源
     */
    return () => {
      // 清除定時器
      clearInterval(timer);

      // 清理 NFC 相關資源
      const cleanUp = async () => {
        try {
          // 移除 NFC 事件監聽器
          NfcManager.setEventListener(NfcEvents.DiscoverTag, null);
          // 取消註冊 NFC 標籤事件
          await NfcManager.unregisterTagEvent();
        } catch (error) {
          console.log('清理NFC時發生錯誤:', error);
        }
      };
      cleanUp();
    }
  }, []);

  /**
   * 取得當前班次的巡邏點資料
   * 檢查是否有正在進行中的班次，並觸發時間範圍更新
   */
  const getShiftPrs = async() => {
    let now = new Date();
    // 篩選出當前時間範圍內的班次
    let totalPrs = globalData.org.prs.filter((e: any) => (new Date(e.StartTime)) <= now && (new Date(e.EndTime)) > now);

    if (totalPrs.length > 0) {
      let oneprs = totalPrs[0];
      let newTimeRange = getRangeTime(oneprs);
      // 如果時間範圍有變更，觸發重新計算
      if (newTimeRange !== timeRange) {
        setRangeTrigger(newTimeRange);
      }
    }
  }

  /**
   * 巡邏點資料處理 useEffect
   * 當班次時間範圍或組織巡邏點資料變更時觸發
   * 負責以下工作：
   * 1. 篩選當前班次的所有巡邏點
   * 2. 確定當前使用者的巡邏路線
   * 3. 計算下一個要感應的點
   * 4. 設定掃描權限狀態
   */
  useEffect(() => {
    let now = new Date();

    // 篩選當前時間範圍內的所有巡邏點，並補充點位資訊
    let totalPrs = org.prs.filter((e: any) => {
      // 從組織點位資料中找到對應的點位資訊
      const point = org.points.find((p: any) => p.PointId === e.PointId);
      e.PointName = point ? point.PointName : '';  // 點位名稱
      e.Rfid = point ? point.Rfid : '';            // RFID 標籤 ID
      e.loc = point && point.lat && point.lng;     // 是否有位置資訊

      // 檢查是否在當前班次時間範圍內
      return ((new Date(e.StartTime)) <= now && (new Date(e.EndTime)) > now);
    });

    // 儲存當前班次的所有巡邏點
    shiftPrs.current = totalPrs;

    // 篩選出當前使用者已分配的巡邏點
    let myprsfiltered = totalPrs.filter((e: any) => {
      return (e.UserId === globalData.userInfo.UserId);
    });

    // 如果使用者沒有分配的巡邏點，嘗試自動分配
    if (myprsfiltered.length == 0) {
      // 尋找未感應且未分配的第一個巡邏點（CheckOrder == 1 表示路線起點）
      myprsfiltered = totalPrs.filter((e: any) => {
        return (!e.CheckTime && !e.UserId && e.CheckOrder == 1);
      });
      console.log('myprs CheckOrder == 1', myprsfiltered);

      if (myprsfiltered.length == 1) {
        // 只有一個可用路線，自動分配整條路線
        const code = myprsfiltered[0].Code;
        myprsfiltered = totalPrs.filter((e: any) => {
          return (e.Code == code);
        });
        console.log('myprs Code == ', code);
        setNextPoint(myprsfiltered[0]);
      } else {
        // 有多個路線可選擇，提示使用者掃描起始點
        let nextprs = { PointName: 'Scan starting point', Rfid: '?' };
        setNextPoint(nextprs);
      }
      setCanScan(true);
    } else {
      // 使用者已有分配的巡邏點
      if (myprsfiltered.every((e: any) => e.CheckTime)) {
        // 所有點都已感應完成，巡邏結束
        setNextPoint({ PointName: 'End Patrol', Rfid: null });
        setCanScan(false);
      } else {
        // 找出下一個未感應的點（按 CheckOrder 排序）
        let nextprs = myprsfiltered.filter((e: any) => !e.CheckTime).sort((a: any, b: any) => a.CheckOrder - b.CheckOrder);

        if (nextprs.length > 0) {
          setNextPoint(nextprs[0]);
        }
        setCanScan(true);
      }
    }

    // 更新當前使用者的巡邏點列表
    setMyprs(myprsfiltered);

    // 設定時間範圍顯示
    if (totalPrs.length > 0) {
      setTimeRange(formatDateTime(new Date(totalPrs[0].StartTime)).substring(11, 16) + ' - ' +
                  formatDateTime(new Date(totalPrs[0].EndTime)).substring(11, 16));
    }
  }, [rangeTrigger, globalData.org.prs]);

  // 註解：導航標題設定（已停用）
  // 原本用於在導航標題顯示網路連線狀態圖示
  // useEffect(() => {
  //   navigation.setOptions({
  //     headerStyle: styles.header,
  //     headerRight: () => (
  //       <MaterialIcons
  //         name={globalData.isOnline ? "wifi" : "wifi-off"}
  //         size={24}
  //         color={globalData.isOnline ? "#4CAF50" : "#F44336"}
  //         style={{ marginRight: 16 }}
  //       />
  //     ),
  //   });
  // }, [navigation, globalData.isOnline]);

  /**
   * 同步巡邏點參考資料
   * 確保 myprsRef 與 myprs 狀態保持同步，供 NFC 回調函式使用
   */
  useEffect(() => {
    myprsRef.current = myprs;
  }, [myprs]);

  /**
   * 除錯模式掃描函式
   * 在開發模式下模擬 NFC 掃描，自動掃描下一個未感應的點
   * 用於測試巡邏流程而不需要實際的 NFC 標籤
   */
  const debugScan = async () => {
    console.log('Debug Scan');
    // 找到下一個未感應的點
    let prs = myprsRef.current.find((e) => e.CheckTime == null);
    if (!!prs) {
      console.log('next points to scan', prs.Rfid);
      startScan(prs.Rfid);
    }
  }

  /**
   * 開始 NFC 掃描流程
   * 這是核心的巡邏點感應處理函式，負責驗證、記錄和處理巡邏點感應
   * @param rfid - 掃描到的 RFID 標籤 ID
   */
  const startScan = async (rfid: string) => {
    // 設定掃描狀態，防止重複掃描
    isScanningRef.current = true;
    console.log('rfid', rfid);
    addLog('startScan-' + rfid);

    // 在當前使用者的巡邏點中尋找對應的 RFID
    let prs = myprsRef.current.find((e) => e.Rfid == rfid);

    // 驗證 RFID 是否有效
    if (!prs) {
      console.log('RFID not found:', myprsRef.current);
      // 檢查是否為組織內的其他點位
      const found = org.points.find((e: any) => e.Rfid == rfid);
      if (!found) {
        console.log(`RFID not found: ${rfid}`, org.points);
      }
      console.log('About to show RFID not found alert', { found, rfid });

      // 顯示錯誤訊息
      alert(!found ? 'RFID not found' : 'wrong location:' + found.PointName);
      setMessage(!found ? 'RFID not found' : 'wrong location:' + found.PointName);
      setIsScanning(false);
      isScanningRef.current = false;
      return;
    }

    // 檢查該點是否已經感應過
    if (!!prs.CheckTime) {
      alert('Already checked');
      setMessage('Already checked');
      setIsScanning(false);
      isScanningRef.current = false;
      return;
    }

    // 檢查是否按順序感應（前面的點必須先感應）
    let prevprs = myprsRef.current.filter((e) => e.Code == prs.Code && !e.CheckTime && e.CheckOrder < prs.CheckOrder);
    if (prevprs.length > 0) {
      alert(globalData._words.missOrd);  // 顯示順序錯誤訊息
      setMessage(globalData._words.missOrd);
      setIsScanning(false);
      isScanningRef.current = false;
      return;
    }

    // 記錄感應時間
    prs.CheckTime = new Date();

    // 如果該點需要位置資訊，取得當前位置
    if (prs.loc) {
      console.log('取得位置***************');
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        let scanLocation = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Low,
          timeInterval: 5000,  // 5秒的時間間隔
          distanceInterval: 10 // 10公尺的距離間隔
        });
        console.log('scanLocation', scanLocation);
        prs.latitude = scanLocation.coords.latitude;
        prs.longitude = scanLocation.coords.longitude;
      }
    }

    // 將感應記錄寫入本地資料庫
    console.log('寫入新感應紀錄', prs)
    try {
      // === 準備巡邏感應記錄資料 ===
      const alertData: AlertData = {
        username: globalData.userInfo.UserId || '',           // 使用者名稱
        alertId: "",                                          // GUID（空值）
        countryId: globalData.userInfo.CountryID || '',       // 國家 ID
        factoryId: globalData.userInfo.FactoryId || '',       // 工廠 ID
        pointId: prs.PointId || '',                           // 點位 ID
        userId: globalData.userInfo.UserId || '',             // 使用者 ID
        checkTime: formatDateTime(prs.CheckTime),             // 感應時間
        alarmType: 0,                                         // 警報類型（0 = 正常巡邏）
        alarmTime: formatDateTime(new Date()),                // 警報時間
        latitude: prs.latitude || 0,                          // 緯度（null 轉為 0）
        longitude: prs.longitude || 0,                        // 經度（null 轉為 0）
        description: '',                                      // 警報描述（空值）
        precId: prs.PrecId || ''                              // 巡邏記錄 ID
      };

      // === 準備照片資料（巡邏感應無照片） ===
      const photos: PhotoData[] = [];

      // === 使用 useDatabase 函數將感應記錄存入資料庫 ===
      const newId = await insertAlertToDb(alertData, photos);
      console.log('巡邏感應記錄已儲存，記錄 ID:', newId);
    } catch (error) {
      console.error('儲存巡邏感應記錄失敗:', error);
      addLog(`儲存巡邏感應記錄失敗: ${error}`);
    }

    // 嘗試上傳感應記錄到伺服器
    await UploadAlert('patrol 310');

    // 標記為未上傳狀態（將由背景程序處理上傳）
    prs.upload = false;

    // 尋找同路線的下一個感應點
    let nextprs = myprsRef.current.find((e: any) => e.Code == prs.Code && !e.CheckTime && e.CheckOrder == prs.CheckOrder + 1);

    // 如果當前點位沒有分配使用者，進行自動分配
    if (!prs.UserId) {
      // 將當前使用者分配給這個點位
      prs.UserId = globalData.userInfo.UserId;

      // 取得同路線的所有點位並分配給當前使用者
      let newMyPrs = shiftPrs.current.filter((e: any) => e.Code == prs.Code);
      newMyPrs.forEach((e: any) => {
        e.UserId = prs.UserId; // 同路線每個點都指定相同的使用者 ID
      });

      // 更新使用者的巡邏點列表
      setMyprs(newMyPrs);

      // 重新尋找下一個感應點
      nextprs = newMyPrs.find((e: any) => e.Code == prs.Code && !e.CheckTime && e.CheckOrder == prs.CheckOrder + 1);
    }

    // 處理下一個感應點的設定
    if (!!nextprs) {
      // 還有下一個點要感應
      setNextPoint(nextprs);
      setCanScan(true);
    } else {
      // 路線已完成，準備結束巡邏
      nextprs = { PointName: 'End Patrol', Rfid: null };
      setCanScan(false);

      console.log('結束巡邏', prs);
      // 延遲 6 秒後自動結束巡邏（給使用者時間看到完成訊息）
      setTimeout(() => {
        endPatrol(prs);
      }, 6000);
    }

    // 更新下一個感應點顯示
    setNextPoint(nextprs);
    console.log('下次巡邏點 nextPoint', nextprs);

    // 重置掃描狀態
    setIsScanning(false);
    setMessage('OK');
    isScanningRef.current = false;
  }

  /**
   * 刷新檢查時間函式（目前未使用）
   * 用於清除之前設定的計時器
   */
  const refreshCheckTime = async () => {
    // 清除之前的計時器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }

  /**
   * 警報圖示輪播效果
   * 每 5 秒自動切換警報圖示，提供視覺提示
   */
  useEffect(() => {
    const imageInterval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % alarmImages.length);
    }, 5000); // 每 5 秒切換一次

    // 清理函式：元件卸載時清除計時器
    return () => clearInterval(imageInterval);
  }, []);

  /**
   * GridItem - 巡邏點列表項目元件
   * 顯示單個巡邏點的資訊，包括順序、名稱、感應時間和上傳狀態
   *
   * @param props - 元件屬性
   * @param props.item - 巡邏點資料物件
   * @param props.count - 巡邏點總數（用於調整字體大小）
   */
  const GridItem = ({ item, count }:{item: any, count: number}) => {
    return (
      <View style={styles.gridItem}>
        <View style={styles.fieldContainer}>
          {/* 巡邏點順序和名稱 */}
          <Text style={[
            count > 10 ? styles.pointText2 : styles.pointText,  // 根據總數調整字體大小
            item.CheckTime ? styles.checked : (item.PrecId == nextPoint.PrecId ? styles.waiting : {}),  // 狀態顏色
            { flex: 1 }
          ]}>
            {item.CheckOrder + '. ' + item.PointName}
          </Text>

          {/* 感應時間顯示 */}
          <Text style={[
            count > 10 ? styles.pointText2 : styles.pointText,
            item.CheckTime ? styles.checked : {},
            { flex: .3 }
          ]}>
            {item.CheckTime ? formatDateTime(new Date(item.CheckTime)).substring(11, 16) : ''}
          </Text>

          {/* 上傳狀態圖示 */}
          {item.CheckTime && (
            <MaterialIcons
              name={item.upload ? "check-circle" : "wifi-off"}  // 已上傳：勾選圖示，未上傳：離線圖示
              size={20}
              color={item.upload ? "#4CAF50" : "#F44336"}       // 已上傳：綠色，未上傳：紅色
              style={{ width: 22 }}
            />
          )}
        </View>
      </View>
    );
  };

  /**
   * 主要 UI 渲染
   * 包含班次時間顯示、下一個感應點提示、巡邏點列表、狀態訊息和功能按鈕
   */
  return (
    <View style={styles.container}>
      {/* 註解：使用者名稱顯示（已停用）
      <View style={styles.textContainer}>
        <Text style={[styles.text, { flex: 1 }]}>{globalData.userInfo.UserName}</Text>
      </View> */}

      {/* 班次時間範圍顯示 */}
      <View style={styles.textContainer}>
        <Text style={[styles.text]}>{globalData._words.checkTime}：</Text>
        <Text style={[styles.text, { color: 'blue', flex: 1 }]}>{timeRange}</Text>
      </View>

      {/* 註解：下一個感應點顯示（舊版本，已停用）
      <View style={styles.textContainer}>
        <Text style={[styles.text]}>{globalData._words.nextPoint}：</Text>
        <Text style={[styles.text, { color: 'red', flex: 1 }]}>{nextPoint.PointName}</Text>
      </View> */}

      {/* 下一個感應點標題 */}
      <View style={styles.textContainer}>
        <Text style={[styles.text, { flex: 1 }]}>{globalData._words.nextPoint}：</Text>
      </View>

      {/* 下一個感應點名稱（置中顯示） */}
      <View style={styles.textContainer}>
        <Text style={[styles.text, { color: 'red', flex: 1, textAlign: 'center' }]}>
          {nextPoint.PointName}
        </Text>
      </View>

      {/* 巡邏點列表滾動區域 */}
      <ScrollView contentContainerStyle={styles.scrollViewContent}>
        <View style={styles.grid}>
          {myprs.map((item: any) => (
            <GridItem
              key={item.PrecId}
              item={item}
              count={myprs.length}
            />
          ))}
        </View>
      </ScrollView>

      {/* 狀態訊息顯示區域 */}
      <View style={styles.buttonContainer}>
        {/* 註解：手動掃描按鈕（已停用）
        <TouchableOpacity
          style={[styles.button, (isScanning || !canScan) && styles.buttonDisabled]}
          onPress={() => startScan()}
          disabled={isScanning}
        >
          <Text style={styles.buttonText}>{globalData._words.arriveScan}</Text>
        </TouchableOpacity> */}

        {/* 註解：警報按鈕（已停用）
        <TouchableOpacity
          style={[styles.button, isScanning && styles.buttonDisabled]}
          onPress={() => navigation.navigate('Alarm' as never)}
          disabled={isScanning || !canScan}
        >
          <Text style={styles.buttonText}>{globalData._words.alert}</Text>
        </TouchableOpacity> */}

        {/* 應用程式狀態訊息 */}
        <Text style={{fontSize: 13, textAlign: 'center', width: '100%'}} numberOfLines={4}>
          {appMessage}
        </Text>

        {/* 使用者操作回饋訊息 */}
        <Text style={{fontSize: 14, textAlign: 'center', width: '100%'}}>
          {message}
        </Text>
      </View>

      {/* 開發模式除錯按鈕 */}
      {__DEV__ && (
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button]}
            onPress={() => {
              debugScan();
            }}
          >
            <Text style={styles.buttonText}>
              {isScanning ? globalData._words.stopScan : globalData._words.startScan}
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {/* 浮動警報按鈕（右下角） */}
      <TouchableOpacity
        onPress={() => router.push('/alarm')}
        style={{position:'absolute', right: 12, bottom: 30}}
      >
        <Image
          source={alarmImages[currentImageIndex]}
          style={{width: 44, height: 40, borderRadius: 22}}
        />
      </TouchableOpacity>
    </View>
  );
};

/**
 * 樣式定義
 * 定義巡邏畫面的所有 UI 元件樣式
 */
const styles = StyleSheet.create({
  // 導航標題樣式（已停用）
  header: {
    height: 85,
    backgroundColor: 'ivory',
  },

  // 主容器樣式
  container: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    padding: 0,
    backgroundColor: '#afd4f3',  // 淺藍色背景
  },

  // 文字容器樣式（用於班次時間和下一個感應點顯示）
  textContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    backgroundColor: '#fff',      // 白色背景
    paddingLeft: 4,
    paddingBottom: 2,
  },

  // 基本文字樣式
  text: {
    // 基本文字樣式，可由其他樣式覆蓋
  },

  // 按鈕容器樣式
  buttonContainer: {
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingTop: 15,
    paddingBottom: 15,
    minHeight: 100,
  },

  // 按鈕樣式
  button: {
    backgroundColor: '#cdeb87',   // 淺綠色背景
    padding: 10,
    borderRadius: 5,
    minWidth: '60%',
    alignItems: 'center',
  },

  // 按鈕文字樣式
  buttonText: {
    fontSize: 16,
  },

  // 滾動視圖內容樣式
  scrollViewContent: {
    flexGrow: 1,
  },

  // 巡邏點網格容器樣式
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'flex-start',
    flex: 1,
    minWidth: '100%',
    backgroundColor: '#f0f0f0',   // 淺灰色背景
    paddingLeft: 10,
    paddingTop: 5,
    minHeight: 300,
  },

  // 巡邏點項目樣式
  gridItem: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 3,
    borderBottomWidth: 1,
    borderBottomColor: '#aaa',    // 分隔線顏色
  },

  // 巡邏點欄位容器樣式
  fieldContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },

  // 巡邏點文字樣式（正常大小）
  pointText: {
    fontSize: 14,
    marginHorizontal: 1,
    textAlign: 'left',
    flex: 1,
    color: 'blue'              // 藍色文字
  },

  // 巡邏點文字樣式（較小字體，用於多點位時）
  pointText2: {
    fontSize: 12,
    marginHorizontal: 1,
    textAlign: 'left',
    flex: 1,
    color: 'blue'              // 藍色文字
  },

  // 已感應狀態樣式
  checked: {
    color: 'green',            // 綠色表示已完成
  },

  // 等待感應狀態樣式
  waiting: {
    color: 'red',              // 紅色表示下一個要感應的點
  },

  // 按鈕停用狀態樣式
  buttonDisabled: {
    backgroundColor: '#cccccc', // 灰色背景
    opacity: 0.7,              // 半透明效果
  },
});

export default PatrolScreen;
