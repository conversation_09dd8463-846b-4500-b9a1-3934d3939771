import React, { useEffect, useState } from 'react';
import { StyleSheet, TouchableOpacity, TextInput, Alert, Linking, BackHandler } from 'react-native';
import { useAppContext } from './context/AppContext';
import { Text, View } from '@/components/Themed';

/**
 * LoginScreen元件的屬性介面
 * @interface LoginScreenProps
 * @property {Function} setIsLogin - 更新登入狀態的回調函數
 */
interface LoginScreenProps {
  setIsLogin: (value: boolean) => void;
}

/**
 * LoginScreen元件處理使用者認證
 * @component
 * @param {LoginScreenProps} props - 元件屬性
 * @returns {JSX.Element} 登入畫面UI
 */
const LoginScreen: React.FC<LoginScreenProps> = ({ setIsLogin }) => {
  const { db, globalData, setGlobalData, settings, addLog } = useAppContext();
  const [username, setUsername] = useState(globalData.userInfo?.secret?.username || '');
  const [password, setPassword] = useState(globalData.userInfo?.secret?.password || '');
  const [showLoginButton, setShowLoginButton] = useState(true);
  // const testPrs = async() => {
  //   let now = new Date();
  //   let totalPrs = globalData.org.prs.filter((e: any) => (new Date(e.StartTime)) <= now && (new Date(e.EndTime)) > now);
  //   totalPrs.forEach((e: any) => {
  //     e.CheckTime = now;
  //     e.UserId = '999';
  //     e.EstUserId = '999';
  //     console.log(`set dummy checktime ${e.CheckTime} of ${e.PrecId}`);
  //     console.log(` ${new Date(e.StartTime)}, ${e.StartTime} - ${e.EndTime}, ${new Date(e.EndTime)}, ${e.StartTime}`);
  //   });
  //   setGlobalData({
  //     ...globalData,
  //     org: {
  //       ...globalData.org,
  //       prs: totalPrs,
  //     },
  //   });
  // }

  /**
 * 處理登入流程，包括驗證、API呼叫和錯誤處理
 * @async
 * @function handleLogin
 */
const handleLogin = async () => {
    // Validate input fields
    if (username === '' || password === '') {
      Alert.alert('請輸入帳號和密碼');
      return;
    }

    setShowLoginButton(false);

    const maxRetries = 3;
    let retryCount = 0;

    /**
     * Attempts to login with retry mechanism
     * @async
     * @function tryLogin
     * @returns {Promise<Object>} Response data from login API
     * @throws {Error} When login fails after max retries
     */
    const tryLogin = async () => {
      while (retryCount < maxRetries) {
        try {
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('請求超時')), 10000);
          });

          console.log('login', settings.apiUrl + "/AppLogin");


          const loginPromise = fetch(settings.apiUrl + "/AppLogin", {
            method: 'POST',
            headers: {
              ...settings.httpHeader,
              'Content-Type': 'application/json',
            },

            body: JSON.stringify({
              username: username, 
              password: password,
              version: globalData.version,
              serial: globalData.serial
            }),
          })
          .then(response => {
            console.log('Response status:', response.status);
            return response;
          })
          .catch(error => {
            console.error('Network error:', error);
            console.error('API URL:', settings.apiUrl + "/AppLogin");
            console.error('Request headers:', settings.httpHeader);
            throw error;

          });

          const response1 = await Promise.race([loginPromise, timeoutPromise]) as Response;
          // console.log('response1', response1);
          if (!response1.ok) {
            throw new Error('登入失敗');
          }
          const data = await response1.json();
          return data;
        } catch (error) {
          retryCount++;
          if (retryCount === maxRetries) {
            throw error;
          }
          console.log(`登入失敗，第 ${retryCount} 次重試`);
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      }
    };

    /**
 * Handles post-login success operations including data updates and navigation
 * @async
 * @function afterSuccessLogin
 * @param {Object} retObj - Response object from login API
 */
const afterSuccessLogin = async (retObj: any) => {
      // Check database availability
      if (!db) {
        alert('database not ready');
        BackHandler.exitApp();
        return;
      }
      
      console.log('登入成功');
      addLog("login success !");

      // Mark PRs as uploaded if they have CheckTime
      if (Array.isArray(retObj.org.prs)) {
        retObj.org.prs.forEach((e: any) => {
          e.upload = !!e.CheckTime;
        })
      }

      // 更新 globalData 中的使用者資訊
      setGlobalData({
        ...globalData,
        userInfo: {
          ...globalData.userInfo,
          secret: {
            username,
            password,
          },
          isLogin: true,
          username: retObj.username,
          CountryId: retObj.CountryId,
          RegionId: retObj.RegionId,
          FactoryId: retObj.FactoryId,
          UserId: retObj.UserId,
          UserName: retObj.password,
          Name: retObj.username,
          culture: retObj.culture,
          role: retObj.role,
          Admin: retObj.role === "Admin",
        },
        isOnline: true,
        isLogin: true,
        org: retObj.org,
        _words: retObj.words,
      });

      await db.runAsync('update userInfo set info=?,words=?', 
        [JSON.stringify(globalData.userInfo), JSON.stringify(globalData._words)]);

      // console.log('username', globalData.userInfo.username);
      setIsLogin(true);
    }

    try {
      const retObj = await tryLogin();
      console.log('retObj.appApk :', retObj.appApk);
      setShowLoginButton(true);
      
      // Check if login was successful
      if (!retObj.isLogin) {
        alert(retObj.message);
        return;
      }

      // Handle different response actions from server
      if ((retObj.action == "1" || retObj.action == "2") && !!retObj.appApk) {
          // Show update available dialog with confirm/cancel options
          Alert.alert(
            "Patrolling",
            retObj.message,
            [
              { 
                text: globalData._words.confirm,
                onPress: async () => {
                  Linking.openURL(retObj.appApk);
                  BackHandler.exitApp();
                }
              }, { 
                text: globalData._words.cancel,
                onPress: async () => {
                  if (retObj.action == "1") {
                      BackHandler.exitApp();
                  } else {
                      afterSuccessLogin(retObj);
                  }
                }
              }
            ]
          );
      } else {
          // Handle other response scenarios
          if (!!retObj.message) {
              // Show message dialog if message exists
              Alert.alert(
                "Patrolling",
                retObj.message,
                [
                  { 
                    text: globalData._words.confirm,
                    onPress: async () => {
                      if (retObj.action == "1") {
                          BackHandler.exitApp(); // action=1, exit application
                      } else {
                          afterSuccessLogin(retObj);
                      }
                    }
                  }
                ]
              );
          } else {
              // No message, just handle action
              if (retObj.action == "1") {
                  BackHandler.exitApp(); // action=1, exit application
              } else {
                  afterSuccessLogin(retObj);
              }
          }    
      }
      // addLog("retObj.isLogin: " + retObj.isLogin);
      // if (!retObj.isLogin) {
      //   setShowLoginButton(true);
      //   setIsLogin(false);
      //   addLog("setIsLogin(false) - return");
      //   return;
      // }

  
      // const result = await db.getFirstAsync('SELECT max(alertId) as id FROM alertfiles');
      // console.log('maxid', result);

      // db.runAsync(
      //   "INSERT INTO alertfiles (alertId, path, PhotoId, time, Latitude, Longitude) VALUES " +
      //   "(?, '/path/to/photo.jpg', 'PHOTO123', datetime('now'), 25.0330, 121.5654)",
      //   [(result as {id: number}).id + 1]
      // );
  

    } catch (error: unknown) {
      // Error handling for login process
      setShowLoginButton(true);
      console.error('錯誤:', error);
      
      if (error instanceof Error) {
        if (error.message === '請求超時') {
          alert('Unable to connect to the server, please check your network connection');
        } else {
          alert(`Login failed: ${error.message}`);
        }
      } else {
        alert('An unknown error occurred');
      }
      return;
    }
  };

  return (
    <View style={styles.container}>
      {/* Username input field */}
      <Text>帳號 / username</Text>
      <TextInput
        style={styles.input}
        placeholder=""
        value={username}
        onChangeText={(text) => {
          setUsername(text);
        }}
        keyboardType="visible-password"
        autoCapitalize="none"
      />
      
      {/* Password input field */}
      <Text style={{marginTop: 10}}>密碼 / password</Text>
      <TextInput
        style={styles.input}
        placeholder=""
        value={password}
        onChangeText={(text) => {
          setPassword(text);
        }}
        keyboardType="ascii-capable"
        autoCapitalize="none"
        secureTextEntry={true}
      />
      
      {/* Login button */}
      <TouchableOpacity 
        style={[styles.button, !showLoginButton && styles.disabledButton]}
        onPress={handleLogin}
        disabled={!showLoginButton}
      >
        <Text style={styles.buttonText}>
          登入 / Login
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingTop: 10,
    backgroundColor: 'transparent',
  },
  button: {
    marginTop: 30,
    backgroundColor: '#cdeb87',
    padding: 10,
    borderRadius: 8,
    width: 200,
  },
  buttonText: {
    color: '#333',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
  },
  input: {
    width: 200,
    height: 45,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    paddingHorizontal: 10,
    marginVertical: 10,
    textAlign: 'center',
    color: '#333',
    backgroundColor: '#f0f0f0',
  },
  disabledButton: {
    backgroundColor: '#cccccc',
    opacity: 0.7,
  },
});

export default LoginScreen;
