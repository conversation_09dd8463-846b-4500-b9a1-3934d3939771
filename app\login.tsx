// React 核心函式庫和 Hooks
import React, { useEffect, useState } from 'react';
// React Native 基礎 UI 元件和系統功能
import { StyleSheet, TouchableOpacity, TextInput, Alert, Linking, BackHandler } from 'react-native';
// 應用程式全域狀態管理
import { useAppContext } from './context/AppContext';
// 主題化 UI 元件
import { Text, View } from '@/components/Themed';

/**
 * LoginScreen 元件的屬性介面定義
 *
 * @interface LoginScreenProps
 * @property {Function} setIsLogin - 更新登入狀態的回調函式，用於通知父元件登入成功
 */
interface LoginScreenProps {
  setIsLogin: (value: boolean) => void;
}

/**
 * LoginScreen - 使用者登入認證元件
 *
 * 功能說明：
 * 1. 提供使用者名稱和密碼輸入介面
 * 2. 處理登入 API 請求和回應
 * 3. 實作重試機制和超時控制
 * 4. 處理應用程式更新檢查和下載
 * 5. 管理登入狀態和錯誤處理
 * 6. 儲存使用者認證資訊到本地資料庫
 *
 * @component
 * @param {LoginScreenProps} props - 元件屬性
 * @returns {JSX.Element} 登入畫面 UI
 */
const LoginScreen: React.FC<LoginScreenProps> = ({ setIsLogin }) => {
  // 從全域狀態取得必要的函式和資料
  const {
    db,              // SQLite 資料庫實例
    globalData,      // 全域資料（使用者資訊、組織資料等）
    setGlobalData,   // 更新全域資料函式
    settings,        // 應用程式設定（API URL、HTTP 標頭等）
    addLog           // 新增日誌函式
  } = useAppContext();

  // 狀態管理
  const [username, setUsername] = useState(globalData.userInfo?.secret?.username || '');  // 使用者名稱（從儲存的資料載入）
  const [password, setPassword] = useState(globalData.userInfo?.secret?.password || '');  // 密碼（從儲存的資料載入）
  const [showLoginButton, setShowLoginButton] = useState(true);                           // 控制登入按鈕顯示狀態
  // 註解：測試用的巡邏點資料設定函式（已停用）
  // 用於開發階段模擬巡邏點已完成的狀態
  // const testPrs = async() => {
  //   let now = new Date();
  //   let totalPrs = globalData.org.prs.filter((e: any) => (new Date(e.StartTime)) <= now && (new Date(e.EndTime)) > now);
  //   totalPrs.forEach((e: any) => {
  //     e.CheckTime = now;        // 設定感應時間為當前時間
  //     e.UserId = '999';         // 設定使用者 ID
  //     e.EstUserId = '999';      // 設定預估使用者 ID
  //     console.log(`set dummy checktime ${e.CheckTime} of ${e.PrecId}`);
  //     console.log(` ${new Date(e.StartTime)}, ${e.StartTime} - ${e.EndTime}, ${new Date(e.EndTime)}, ${e.StartTime}`);
  //   });
  //   setGlobalData({
  //     ...globalData,
  //     org: {
  //       ...globalData.org,
  //       prs: totalPrs,
  //     },
  //   });
  // }

  /**
   * 處理登入流程的主要函式
   *
   * 執行流程：
   * 1. 驗證輸入欄位是否完整
   * 2. 停用登入按鈕防止重複提交
   * 3. 執行帶有重試機制的登入請求
   * 4. 處理伺服器回應和各種情境
   * 5. 更新全域狀態和本地資料庫
   * 6. 處理應用程式更新檢查
   *
   * @async
   * @function handleLogin
   */
  const handleLogin = async () => {
    // === 輸入驗證 ===
    if (username === '' || password === '') {
      Alert.alert('請輸入帳號和密碼');
      return;
    }

    // 停用登入按鈕，防止重複提交
    setShowLoginButton(false);

    // 重試機制設定
    const maxRetries = 3;        // 最大重試次數
    let retryCount = 0;          // 當前重試計數

    /**
     * 執行登入請求的內部函式（包含重試機制）
     *
     * 功能說明：
     * 1. 使用 AbortController 實作請求超時控制
     * 2. 發送 POST 請求到登入 API
     * 3. 處理網路錯誤和超時情況
     * 4. 實作自動重試機制（最多 3 次）
     * 5. 在重試間隔中等待 3 秒
     *
     * @async
     * @function tryLogin
     * @returns {Promise<Object>} 登入 API 回應的資料物件
     * @throws {Error} 當達到最大重試次數後仍然失敗時拋出錯誤
     */
    const tryLogin = async () => {
      while (retryCount < maxRetries) {
        try {
          // === 建立請求超時控制 ===
          const abortController = new AbortController();
          const timeoutId = setTimeout(() => {
            abortController.abort();  // 10 秒後自動取消請求
          }, 10000);

          console.log('login', settings.apiUrl + "/AppLogin");

          try {
            // === 發送登入 API 請求 ===
            const response1 = await fetch(settings.apiUrl + "/AppLogin", {
              method: 'POST',
              headers: {
                ...settings.httpHeader,           // 應用程式預設標頭
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                username: username,               // 使用者輸入的帳號
                password: password,               // 使用者輸入的密碼
                version: globalData.version,      // 應用程式版本
                serial: globalData.serial         // 裝置序號
              }),
              signal: abortController.signal,     // 綁定 AbortController 信號
            });

            // 請求完成，清除超時計時器
            clearTimeout(timeoutId);

            console.log('Response status:', response1.status);

            // === 檢查 HTTP 回應狀態 ===
            if (!response1.ok) {
              throw new Error('登入失敗');
            }

            // 解析 JSON 回應資料
            const data = await response1.json();
            return data;

          } catch (fetchError) {
            // 確保清除超時計時器
            clearTimeout(timeoutId);

            // === 錯誤類型判斷 ===
            if (fetchError instanceof Error && fetchError.name === 'AbortError') {
              // 請求被 AbortController 取消（超時）
              throw new Error('請求超時');
            }

            // 記錄網路錯誤詳細資訊
            console.error('Network error:', fetchError);
            console.error('API URL:', settings.apiUrl + "/AppLogin");
            console.error('Request headers:', settings.httpHeader);
            throw fetchError;
          }
        } catch (error) {
          // === 重試機制處理 ===
          retryCount++;
          if (retryCount === maxRetries) {
            // 達到最大重試次數，拋出最後的錯誤
            throw error;
          }
          console.log(`登入失敗，第 ${retryCount} 次重試`);
          // 等待 3 秒後進行下一次重試
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      }
    };

    /**
     * 處理登入成功後的後續操作
     *
     * 功能說明：
     * 1. 檢查資料庫可用性
     * 2. 標記已感應的巡邏點為已上傳狀態
     * 3. 更新全域狀態中的使用者資訊
     * 4. 將使用者資訊和多語言文字儲存到本地資料庫
     * 5. 通知父元件登入成功
     *
     * @async
     * @function afterSuccessLogin
     * @param {Object} retObj - 登入 API 回應的資料物件
     */
    const afterSuccessLogin = async (retObj: any) => {
      // === 資料庫可用性檢查 ===
      if (!db) {
        alert('database not ready');
        BackHandler.exitApp();  // 資料庫未準備好時退出應用程式
        return;
      }

      console.log('登入成功');
      addLog("login success !");

      // === 處理巡邏點上傳狀態 ===
      // 將已有感應時間的巡邏點標記為已上傳
      if (Array.isArray(retObj.org.prs)) {
        retObj.org.prs.forEach((e: any) => {
          e.upload = !!e.CheckTime;  // 有 CheckTime 表示已感應，標記為已上傳
        })
      }

      // === 更新全域狀態 ===
      setGlobalData({
        ...globalData,
        userInfo: {
          ...globalData.userInfo,
          secret: {
            username,                    // 儲存帳號密碼供下次自動填入
            password,
          },
          isLogin: true,                 // 登入狀態
          username: retObj.username,     // 伺服器回傳的使用者名稱
          CountryId: retObj.CountryId,   // 國家 ID
          RegionId: retObj.RegionId,     // 區域 ID
          FactoryId: retObj.FactoryId,   // 工廠 ID
          UserId: retObj.UserId,         // 使用者 ID
          UserName: retObj.password,     // 使用者顯示名稱
          Name: retObj.username,         // 使用者名稱
          culture: retObj.culture,       // 文化設定
          role: retObj.role,             // 使用者角色
          Admin: retObj.role === "Admin", // 是否為管理員
        },
        isOnline: true,                  // 線上狀態
        isLogin: true,                   // 登入狀態
        org: retObj.org,                 // 組織資料（巡邏點、路線等）
        _words: retObj.words,            // 多語言文字資料
      });

      // === 儲存到本地資料庫 ===
      await db.runAsync('update userInfo set info=?,words=?',
        [JSON.stringify(globalData.userInfo), JSON.stringify(globalData._words)]);

      // 通知父元件登入成功，切換到主畫面
      setIsLogin(true);
    }

    try {
      // === 執行登入請求 ===
      const retObj = await tryLogin();
      console.log('retObj.appApk :', retObj.appApk);
      setShowLoginButton(true);  // 重新啟用登入按鈕

      // === 檢查登入是否成功 ===
      if (!retObj.isLogin) {
        alert(retObj.message);
        return;
      }

      // === 處理伺服器回應的不同情境 ===

      // 情境 1：需要應用程式更新（action=1 強制更新，action=2 可選更新）
      if ((retObj.action == "1" || retObj.action == "2") && !!retObj.appApk) {
          Alert.alert(
            "Patrolling",
            retObj.message,  // 顯示更新訊息
            [
              {
                text: globalData._words.confirm,  // 確認更新
                onPress: async () => {
                  Linking.openURL(retObj.appApk);  // 開啟下載連結
                  BackHandler.exitApp();           // 退出應用程式
                }
              }, {
                text: globalData._words.cancel,   // 取消更新
                onPress: async () => {
                  if (retObj.action == "1") {
                      // action=1 為強制更新，取消則退出應用程式
                      BackHandler.exitApp();
                  } else {
                      // action=2 為可選更新，取消則繼續登入
                      afterSuccessLogin(retObj);
                  }
                }
              }
            ]
          );
      } else {
          // 情境 2：其他回應情境處理
          if (!!retObj.message) {
              // 有訊息需要顯示給使用者
              Alert.alert(
                "Patrolling",
                retObj.message,
                [
                  {
                    text: globalData._words.confirm,
                    onPress: async () => {
                      if (retObj.action == "1") {
                          BackHandler.exitApp();  // action=1 退出應用程式
                      } else {
                          afterSuccessLogin(retObj);  // 其他情況繼續登入流程
                      }
                    }
                  }
                ]
              );
          } else {
              // 沒有訊息，直接根據 action 處理
              if (retObj.action == "1") {
                  BackHandler.exitApp();  // action=1 退出應用程式
              } else {
                  afterSuccessLogin(retObj);  // 其他情況繼續登入流程
              }
          }
      }

      // 註解：測試用的資料庫操作（已停用）
      // 用於測試警報檔案表的最大 ID 查詢和插入操作
      // const result = await db.getFirstAsync('SELECT max(alertId) as id FROM alertfiles');
      // console.log('maxid', result);
      // db.runAsync(
      //   "INSERT INTO alertfiles (alertId, path, PhotoId, time, Latitude, Longitude) VALUES " +
      //   "(?, '/path/to/photo.jpg', 'PHOTO123', datetime('now'), 25.0330, 121.5654)",
      //   [(result as {id: number}).id + 1]
      // );

    } catch (error: unknown) {
      // === 登入流程錯誤處理 ===
      setShowLoginButton(true);  // 重新啟用登入按鈕
      console.error('錯誤:', error);

      // 根據錯誤類型顯示不同的錯誤訊息
      if (error instanceof Error) {
        if (error.message === '請求超時') {
          // 網路連線超時錯誤
          alert('Unable to connect to the server, please check your network connection');
        } else {
          // 其他已知錯誤
          alert(`Login failed: ${error.message}`);
        }
      } else {
        // 未知錯誤類型
        alert('An unknown error occurred');
      }
      return;
    }
  };

  /**
   * 主要 UI 渲染
   * 包含帳號密碼輸入欄位和登入按鈕
   */
  return (
    <View style={styles.container}>
      {/* 帳號輸入區域 */}
      <Text>帳號 / username</Text>
      <TextInput
        style={styles.input}
        placeholder=""
        value={username}
        onChangeText={(text) => {
          setUsername(text);
        }}
        keyboardType="visible-password"  // 顯示所有字元（不隱藏）
        autoCapitalize="none"            // 不自動大寫
      />

      {/* 密碼輸入區域 */}
      <Text style={{marginTop: 10}}>密碼 / password</Text>
      <TextInput
        style={styles.input}
        placeholder=""
        value={password}
        onChangeText={(text) => {
          setPassword(text);
        }}
        keyboardType="ascii-capable"     // ASCII 字元鍵盤
        autoCapitalize="none"            // 不自動大寫
        secureTextEntry={true}           // 隱藏密碼輸入
      />

      {/* 登入按鈕 */}
      <TouchableOpacity
        style={[
          styles.button,
          !showLoginButton && styles.disabledButton  // 登入中時顯示停用樣式
        ]}
        onPress={handleLogin}
        disabled={!showLoginButton}      // 登入中時停用按鈕
      >
        <Text style={styles.buttonText}>
          登入 / Login
        </Text>
      </TouchableOpacity>
    </View>
  );
};

/**
 * 樣式定義
 * 定義登入畫面的所有 UI 元件樣式
 */
const styles = StyleSheet.create({
  // 主容器樣式
  container: {
    flex: 1,
    justifyContent: 'flex-start',    // 內容從頂部開始排列
    alignItems: 'center',            // 水平置中對齊
    paddingTop: 10,                  // 頂部間距
    backgroundColor: 'transparent',   // 透明背景
  },

  // 登入按鈕樣式
  button: {
    marginTop: 30,                   // 頂部間距
    backgroundColor: '#cdeb87',      // 淺綠色背景
    padding: 10,                     // 內部間距
    borderRadius: 8,                 // 圓角
    width: 200,                      // 固定寬度
  },

  // 按鈕文字樣式
  buttonText: {
    color: '#333',                   // 深灰色文字
    textAlign: 'center',             // 文字置中
    fontSize: 16,                    // 字體大小
    fontWeight: 'bold',              // 粗體
  },

  // 輸入欄位樣式
  input: {
    width: 200,                      // 固定寬度
    height: 45,                      // 固定高度
    borderWidth: 1,                  // 邊框寬度
    borderColor: '#ccc',             // 淺灰色邊框
    borderRadius: 5,                 // 圓角
    paddingHorizontal: 10,           // 水平內部間距
    marginVertical: 10,              // 垂直外部間距
    textAlign: 'center',             // 文字置中
    color: '#333',                   // 深灰色文字
    backgroundColor: '#f0f0f0',      // 淺灰色背景
  },

  // 按鈕停用狀態樣式
  disabledButton: {
    backgroundColor: '#cccccc',      // 灰色背景
    opacity: 0.7,                   // 半透明效果
  },
});

// 匯出 LoginScreen 元件作為預設匯出
export default LoginScreen;
