import { StatusBar } from 'expo-status-bar';
import { Keyboard, StyleSheet, TouchableOpacity, Pressable, Image, Modal, useColorScheme } from 'react-native';
import { Text, View } from '@/components/Themed';
import { type NavigationProp } from '@react-navigation/native';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useAppContext } from './context/AppContext';
import { useEffect, useState } from 'react';
import Constants from 'expo-constants';
import LoginScreen from './login';
import SelectFactory from './selectFactory';
import { router } from 'expo-router';

function NetStatus() {
  const { globalData } = useAppContext();
  return (
    <MaterialIcons 
    name={globalData.isOnline ? "wifi" : "wifi-off"} 
    size={24} 
    color={globalData.isOnline ? "#4CAF50" : "#F44336"}
    style={{ marginRight: 16 }}
  />
  );
}

// 主頁面組件
export default function HomeScreen() {
    // 狀態管理
    const colorScheme = useColorScheme();
    const [isLogin, setIsLogin] = useState(false); // 登入狀態
    const [isFactoryModalVisible, setIsFactoryModalVisible] = useState(false); // 工廠選擇模態框可見性
    const { globalData, setGlobalData, alertQueue, settings } = useAppContext(); // 全局數據
    const [clicks, setClicks] = useState<number[]>([]); // 點擊記錄（用於調試模式觸發）
    const [isDebug, setIsDebug] = useState(false); // 調試模式狀態
    const [isLanguageModalVisible, setIsLanguageModalVisible] = useState(false); // 語言選擇模態框可見性
    const [isKeyboardVisible, setIsKeyboardVisible] = useState(false); // 鍵盤可見性

    // 語言選項配置
    const languages = [
        { name: '中文', key: 'zh-TW' },
        { name: 'English', key: 'en-US' },
        { name: 'Tiếng Việt', key: 'vi-VN' },
        { name: 'Bahasa Indonesia', key: 'id-ID' },
        { name: 'ភាសាខ្មែរ', key: 'km-KH' }
    ];

    // 監聽鍵盤顯示/隱藏事件
    useEffect(() => {
        const showSubscription = Keyboard.addListener('keyboardDidShow', handleKeyboardShow);
        const hideSubscription = Keyboard.addListener('keyboardDidHide', handleKeyboardHide);
    
        return () => {
            showSubscription.remove();
            hideSubscription.remove();
        };
    }, []);

    useEffect(() => {
        if (!isLogin) {
            // 如果未登入，重置全局數據，如果isLogin==true，則不需要重置(在login.tsx中處理)
            setGlobalData({
                ...globalData,
                userInfo: {
                ...globalData.userInfo,
                isLogin: isLogin,
                },
                isLogin: isLogin
            });
        }
    }, [isLogin]);

    // 處理鍵盤顯示
    const handleKeyboardShow = () => {
        setIsKeyboardVisible(true);
    };

    // 處理鍵盤隱藏
    const handleKeyboardHide = () => {
        setIsKeyboardVisible(false);
    };

    // 版本號點擊處理（觸發調試模式）
    const handleVersionClick = () => {
        const now = Date.now();
        const recentClicks = [...clicks, now].slice(-5);
        setClicks(recentClicks);
        
        // 如果在2秒內點擊5次，切換調試模式
        if (recentClicks.length === 5 && recentClicks[4] - recentClicks[0] <= 2000) {
            setIsDebug(!isDebug);
            setClicks([]);
        }
    };

    // 導航到警報頁面（處理多工廠情況）
    const navAlarm = () => {
        if (globalData.org.factories.length > 1) {
            setIsFactoryModalVisible(true);
        } else {
            router.push('./alarm');
        }
    }

    // 處理語言選擇
    const handleLanguageSelect = async (langKey: string) => {
        setIsLanguageModalVisible(false);
        
        try {
            // 獲取選定語言的翻譯文本
            const words = await fetchLanguageWords(langKey);
            
            // 更新全局數據中的語言設置
            setGlobalData({
                ...globalData,
                _words: words,
            });
            
        } catch (error) {
            handleLanguageError(error);
        }
    };

    // 獲取語言翻譯文本
    const fetchLanguageWords = async (langKey: string) => {
        const maxRetries = 3;
        let retryCount = 0;
        
        while (retryCount < maxRetries) {
            try {
                const response = await fetchWithTimeout(
                    `${settings.apiUrl}/GetWords`,
                    {
                        method: 'POST',
                        headers: {
                            ...settings.httpHeader,
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ culture: langKey }),
                    },
                    10000
                );

                if (!response.ok) throw new Error('請求失敗');
                const data = await response.json();
                return data.words;
                
            } catch (error) {
                retryCount++;
                if (retryCount === maxRetries) throw error;
                await delay(3000);
            }
        }
    };

    // 帶超時的fetch請求
    const fetchWithTimeout = async (url: string, options: RequestInit, timeout: number) => {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        
        try {
            const response = await fetch(url, {
                ...options,
                signal: controller.signal
            });
            clearTimeout(timeoutId);
            return response;
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    };

    // 延遲函數
    const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

    // 處理語言選擇錯誤
    const handleLanguageError = (error: unknown) => {
        let message = '發生未知錯誤';
        
        if (error instanceof Error) {
            message = error.message === '請求超時' 
                ? '無法連接服務器，請檢查網絡連接' 
                : `請求失敗: ${error.message}`;
        }
        
        alert(message);
    };

    // 渲染主內容
    const renderMainContent = () => {
      if (!isLogin) return <LoginScreen setIsLogin={setIsLogin} />;
      
      return (
          <View style={[styles.buttonContainer, {backgroundColor: 'transparent'}]}>
              <Text>{globalData.userInfo.username}</Text>
              
              {/* 巡檢按鈕（非管理員顯示） */}
              {!globalData.userInfo.Admin && renderButton(
                  globalData._words.patrol, 
                  () => router.push('./patrol')
              )}
              
              {/* 警報按鈕 */}
              {renderButton(globalData._words.alert, navAlarm)}
              
              {/* 設置巡檢點按鈕（管理員顯示） */}
              {globalData.userInfo.Admin && renderButton(
                  globalData._words.setPoint, 
                  () => router.push('./points')
              )}
              
              {/* 修改密碼按鈕（非管理員顯示） */}
              {!globalData.userInfo.Admin && renderButton(
                  globalData._words.changePass, 
                  () => router.push('./changepass')
              )}
              
              {/* 語言選擇按鈕（調試模式顯示） */}
              {isDebug && renderButton(
                  '變更文字', 
                  () => setIsLanguageModalVisible(true)
              )}
              
              {/* 登出按鈕 */}
              {renderButton(
                  globalData._words.signout, 
                  () => setIsLogin(false),
                  styles.logoutButton
              )}
          </View>
      );
  };

    // 渲染按鈕
    const renderButton = (text: string, onPress: () => void, style?: any) => (
      <TouchableOpacity
          style={[styles.button, style]}
          onPress={onPress}
      >
          <Text style={styles.buttonText}>{text}</Text>
      </TouchableOpacity>
  );

  // 渲染調試信息
  const renderDebugInfo = () => {
     
      return (
          <View style={styles.debugContainer}>
        {!isKeyboardVisible && isDebug && (
          <View style={{backgroundColor: 'transparent', alignItems: 'center'}}>
            <Pressable
              style={{marginVertical: 4}}
              onPress={() => router.push('./logs')}
            >
              <Text style={[styles.buttonText, {color: 'maroon'}]}>系統紀錄</Text>
            </Pressable>
            <Pressable
            style={{marginVertical: 10}}
            onPress={() => router.push('./changeUrl')}
          >
            <Text style={[styles.buttonText, {color: 'maroon'}]}>變更伺服器網址</Text>
          </Pressable>
        </View>
        )}

        {!isKeyboardVisible && (
          <View style={{backgroundColor: 'transparent', alignItems: 'center'}}>
            <View style={{backgroundColor: 'transparent', alignItems: 'center'}}>
                <Text style={{fontSize: 16}}>{isDebug ? 'Queue:' + alertQueue.length : ''}</Text>
            </View>

            <View style={{backgroundColor: 'transparent', alignItems: 'center', paddingBottom: 16}}>
            <TouchableOpacity onPress={handleVersionClick}>
                <Text style={{fontSize: 16, marginTop: 10}}>{globalData.AppVersion}</Text>
            </TouchableOpacity>
            </View>
          </View>
        )}
        </View>
      );
  };

  // 渲染語言選擇模態框
  const renderLanguageModal = () => (
      <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>選擇語言</Text>
              {languages.map((lang) => (
                  <TouchableOpacity
                      key={lang.key}
                      style={styles.languageButton}
                      onPress={() => handleLanguageSelect(lang.key)}
                  >
                      <Text style={styles.languageButtonText}>{lang.name}</Text>
                  </TouchableOpacity>
              ))}
              <TouchableOpacity
                  style={[styles.languageButton, styles.cancelButton]}
                  onPress={() => setIsLanguageModalVisible(false)}
              >
                  <Text style={styles.cancelButtonText}>取消</Text>
              </TouchableOpacity>
          </View>
      </View>
  );

    // 處理工廠選擇
    const handleFactorySelect = (factoryId: string) => {
      const factory = globalData.org.factories.find(
          (f: any) => f.FactoryId === factoryId
      );
      
      setGlobalData((prev: any) => ({
          ...prev,
          userInfo: {
              ...prev.userInfo,
              FactoryId: factoryId,
              FactoryName: factory?.FactoryName
          }
      }));

      setIsFactoryModalVisible(false);
      router.push('./alarm');
  };

    // 渲染主界面
    return (
        <View style={[
            colorScheme === 'dark' ? styles.darkContainer : styles.container, 
            { paddingTop: Constants.statusBarHeight }
        ]}>
            {/* 狀態欄 */}
            <StatusBar style="dark" />
            <View style={styles.netStatusContainer}>
                <NetStatus />
            </View>
            
            {/* 標題區域 */}
            <View style={styles.header}>
                <Image 
                    source={require('../assets/images/namelogo.png')} 
                    style={{ width: 240, height: 120 }} 
                    resizeMode="stretch" 
                />
                <Text style={styles.headerTitle}>{globalData._words.appName}</Text>
            </View>
            
            {/* 主內容區域 */}
            {renderMainContent()}
            
            {/* 調試信息區域 */}
            {renderDebugInfo()}
            
            {/* 工廠選擇模態框 */}
            <SelectFactory
                isVisible={isFactoryModalVisible}
                onClose={() => setIsFactoryModalVisible(false)}
                onSelect={handleFactorySelect}
            />
            
            {/* 語言選擇模態框 */}
            <Modal
                visible={isLanguageModalVisible}
                transparent={true}
                animationType="fade"
                onRequestClose={() => setIsLanguageModalVisible(false)}
            >
                {renderLanguageModal()}
            </Modal>
        </View>
    );
}

// 樣式定義
const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'space-around',
        alignItems: 'center',
        backgroundColor: '#afd4f3',
    },
    darkContainer: {
        flex: 1,
        justifyContent: 'space-around',
        alignItems: 'center',
        backgroundColor: '#555',
    },
    netStatusContainer: {
        position: 'absolute',
        top: Constants.statusBarHeight + 10,
        right: 10,
        zIndex: 1,
        backgroundColor: 'transparent',
    },
    header: {
        backgroundColor: 'transparent',
        paddingTop: 10,
        paddingBottom: 10,
        alignItems: 'center',
    },
    headerTitle: {
        marginTop: 10,
        fontSize: 20, 
        fontWeight: 'bold'
    },
    buttonContainer: {
        justifyContent: 'space-around',
        alignItems: 'center',
        width: '100%',
        padding: 10,
        flex: 1,
    },
    button: {
        padding: 10,
        backgroundColor: '#cdeb87',
        alignItems: 'center',
        minWidth: '72%',
        borderRadius: 8,
        marginVertical: 8,
    },
    buttonText: {
        color: '#333',
        textAlign: 'center',
    },
    logoutButton: {
        backgroundColor: 'gold',
    },
    debugContainer: {
        backgroundColor: 'transparent', 
        alignItems: 'center',
        paddingBottom: 16
    },
    debugText: {
        marginVertical: 4,
        color: 'maroon'
    },
    queueText: {
        fontSize: 16,
        marginVertical: 10
    },
    versionText: {
        fontSize: 16, 
        marginTop: 10
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        width: '80%',
        maxWidth: 400,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 15,
        textAlign: 'center',
    },
    languageButton: {
        backgroundColor: '#f0f0f0',
        padding: 15,
        borderRadius: 8,
        marginVertical: 5,
    },
    languageButtonText: {
        fontSize: 16,
        textAlign: 'center',
    },
    cancelButton: {
        backgroundColor: '#ff9999',
        marginTop: 10,
    },
    cancelButtonText: {
        color: 'white',
        fontSize: 16,
        textAlign: 'center',
    },
});
