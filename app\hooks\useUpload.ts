import { useState, useRef } from 'react';

export function useUpload() {

  const upload = async (data: any, apiName: string, httpHeader: any, addLog: Function, CONFIG: any) => {
    let retryCount = 0;
    
    while (retryCount < CONFIG.MAX_RETRIES) {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      try {
        const response = await fetch(apiName, {
          method: 'POST',
          headers: httpHeader,
          body: JSON.stringify(data),
          signal: controller.signal
        });

        clearTimeout(timeoutId);
        
        if (!response.ok) {
          console.log('!response.ok');
          addLog('!response - ' + JSON.stringify(response));
          throw new Error('upload failed');
        }

        return await response.json();
      } catch (error:any) {
        clearTimeout(timeoutId);
        console.log('上傳失敗', error);
        // addLog('請求超時');

        retryCount++;
        console.log(`第 ${retryCount} 次重試`);
        
        // if (retryCount >= CONFIG.MAX_RETRIES) {
        //   throw error;
        // }
        
        addLog('第 ' + retryCount + ' 次重試');
        await new Promise(resolve => setTimeout(resolve, 3000)); // 等待3秒後重試
      }
    }
    console.log('upload 請求超時');
    throw new Error('upload 請求超時');
  };

  return { upload };
}