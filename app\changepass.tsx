import React, { useEffect, useState, useRef, useCallback } from 'react';
import { TextInput, TouchableOpacity, StyleSheet, ActivityIndicator, Alert, useColorScheme } from 'react-native';
import { Text, View } from '@/components/Themed';
import { useAppContext } from './context/AppContext';
import { router } from 'expo-router';

/**
 * @function ChangePassword
 * @description 密碼變更組件，提供使用者輸入舊密碼、新密碼和確認新密碼的功能，並透過 API 進行密碼更新。
 * 根據系統顏色方案調整介面主題。
 * @returns {JSX.Element} 密碼變更介面。
 */
export default function ChangePassword() {
  // 獲取當前系統的顏色方案 (light/dark)
  const colorScheme = useColorScheme();
  // 從應用程式上下文中獲取全域資料、設定和設定更新函數
  const { globalData, settings } = useAppContext();

  // 使用 useRef 儲存輸入框的值，避免不必要的重新渲染
  const passwordRef = useRef(''); // 舊密碼
  const newpwdRef = useRef('');   // 新密碼
  const confpwdRef = useRef('');  // 確認新密碼

  // 狀態變數，控制載入指示器的顯示與否
  const [loading, setLoading] = useState(false);

  // console.log('render changepass'); // 開發時用於追蹤組件渲染

  /**
   * @function useEffect
   * @description 組件載入時清空密碼輸入框的值，確保每次進入頁面時都是空白的。
   */
  useEffect(() => {
    passwordRef.current = '';
    newpwdRef.current = '';
    confpwdRef.current = '';
  }, []); // 空依賴陣列表示只在組件初次載入時執行一次

  /**
   * @function handleChangePassword
   * @description 處理密碼變更的異步函數。執行以下步驟：
   * 1. 檢查新密碼與確認新密碼是否一致。
   * 2. 檢查所有密碼欄位是否都已填寫。
   * 3. 顯示載入指示器。
   * 4. 構建 API 請求 URL。
   * 5. 發送 POST 請求到後端 API 進行密碼更新。
   * 6. 根據 API 回應顯示成功或失敗訊息。
   * 7. 隱藏載入指示器。
   * @returns {Promise<void>}
   */
  const handleChangePassword = useCallback(async () => {
    // 檢查新密碼與確認新密碼是否一致
    if (newpwdRef.current !== confpwdRef.current) {
      Alert.alert(globalData._words.error || "錯誤", globalData._words.pwdMismatch || "新密碼與確認新密碼不一致");
      return;
    }

    // 檢查是否所有欄位都有填寫
    if (!passwordRef.current || !newpwdRef.current || !confpwdRef.current) {
      Alert.alert(globalData._words.error || "錯誤", globalData._words.required || "所有欄位均為必填");
      return;
    }
    
    setLoading(true); // 顯示載入指示器

    try {
      // 組合 API 的完整路徑
      const url = settings.apiUrl + "/AppChangePassword";
      
      // 發送密碼變更請求
      const response = await fetch(url, {
        method: 'POST',
        headers: settings.httpHeader,
        body: JSON.stringify({
          username: globalData.userInfo.secret.username, // 使用者名稱
          password: passwordRef.current,                 // 舊密碼
          newpwd: newpwdRef.current,                     // 新密碼
          confpwd: confpwdRef.current,                   // 確認新密碼
          culture: globalData.userInfo.culture,          // 語系設定
        }),
      });

      // 處理 API 回應
      const responseText = await response.text();
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (jsonError) {
        console.error("解析 JSON 失敗：", jsonError);
        Alert.alert(globalData._words.error || "錯誤", globalData._words.invalidResponse || "伺服器回應格式錯誤");
        return;
      }

      if (response.ok) {
        // 請求成功，顯示後端返回的訊息或預設成功訊息
        Alert.alert(globalData._words.success || "成功", data.message || "密碼更新成功", [
          { text: globalData._words.ok || "確定", onPress: () => router.replace('/') }
        ]);
      } else {
        // 請求失敗，顯示後端返回的錯誤訊息或預設失敗訊息
        Alert.alert(globalData._words.failed || "失敗", data.message || globalData._words.unknownError || "密碼更新失敗: 未知錯誤");
      }
    } catch (error) {
      // 捕獲網路請求或程式碼執行中的錯誤
      console.error("更新密碼錯誤：", error);
      Alert.alert(globalData._words.error || "錯誤", globalData._words.tryAgainLater || "發生錯誤，請稍後再試");
    }
    
    setLoading(false); // 隱藏載入指示器
  }, [globalData, settings]); // 依賴 globalData 和 settings，確保在它們變化時重新建立函數

  return (
    // 根據顏色方案應用不同的容器樣式
    <View style={colorScheme === 'dark' ? styles.darkContainer : styles.container}>
      <View style={styles.form}>
        {/* 舊密碼輸入組 */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>{globalData._words.oldpwd}</Text>
          <TextInput
            secureTextEntry // 隱藏輸入內容
            defaultValue="" // 預設值為空
            onChangeText={(text) => (passwordRef.current = text)} // 更新 useRef 的值
            style={styles.input}
            placeholder={globalData._words.enterOldPwd} // 添加提示文字
          />
        </View>
        {/* 新密碼輸入組 */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>{globalData._words.newpwd}</Text>
          <TextInput
            secureTextEntry
            defaultValue=""
            onChangeText={(text) => (newpwdRef.current = text)}
            style={styles.input}
            placeholder={globalData._words.enterNewPwd}
          />
        </View>
        {/* 確認新密碼輸入組 */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>{globalData._words.confpwd}</Text>
          <TextInput
            secureTextEntry
            defaultValue=""
            onChangeText={(text) => (confpwdRef.current = text)}
            style={styles.input}
            placeholder={globalData._words.confirmNewPwd}
          />
        </View>
        {/* 確認按鈕 */}
        <TouchableOpacity 
          onPress={handleChangePassword} 
          disabled={loading} // 載入中時禁用按鈕
          style={[styles.button, loading && styles.buttonDisabled]} // 根據載入狀態應用樣式
        >
          {loading ? (
            <ActivityIndicator color="#fff" /> // 載入時顯示活動指示器
          ) : (
            <Text style={styles.buttonText}>{globalData._words.confirm}</Text> // 顯示確認文字
          )}
        </TouchableOpacity>
      </View>
      {/* 底部留白，提供更好的視覺效果 */}
      <View style={{ height: '30%' }}></View>
    </View>
  );
}

/**
 * @constant styles
 * @description 定義組件的樣式。
 */
const styles = StyleSheet.create({
  // 主容器樣式 (淺色主題)
  container: {
    flex: 1,
    backgroundColor: '#bfd4f3',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  // 主容器樣式 (深色主題)
  darkContainer: {
    flex: 1,
    backgroundColor: '#555',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  // 表單容器樣式
  form: {
    width: '80%',
    maxWidth: 400,
    backgroundColor: 'transparent',
  },
  // 標題樣式 (目前被註釋掉，但保留樣式定義)
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
    textAlign: 'center',
  },
  // 輸入組 (標籤 + 輸入框) 樣式
  inputGroup: {
    marginBottom: 15,
    backgroundColor: 'transparent',
  },
  // 輸入框標籤樣式
  label: {
    fontSize: 16,
    marginBottom: 5,
  },
  // 輸入框樣式
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 8,
    backgroundColor: '#f0f0f0',
  },
  // 按鈕樣式
  button: {
    backgroundColor: '#007BFF',
    padding: 10,
    borderRadius: 4,
    alignItems: 'center',
    marginTop: 20,
  },
  // 按鈕禁用時的樣式
  buttonDisabled: {
    opacity: 0.7,
  },
  // 按鈕文字樣式
  buttonText: {
    color: '#fff',
    fontSize: 16,
  },
});