import React, { useState } from 'react';
import { useAppContext } from '../context/AppContext';

export default function ChangePassword() {
  const { globalData, settings } = useAppContext();
  const [password, setPassword] = useState('');
  const [newpwd, setNewpwd] = useState('');
  const [confpwd, setConfpwd] = useState('');
  const [loading, setLoading] = useState(false);

  const handleChangePassword = async () => {
    // 檢查新密碼與確認新密碼是否一致
    if (newpwd !== confpwd) {
      alert(globalData._words.pwdMismatch || "新密碼與確認新密碼不一致");
      return;
    }
    // 檢查是否所有欄位都有填寫
    if (!password || !newpwd || !confpwd) {
      alert(globalData._words.required || "所有欄位均為必填");
      return;
    }

    setLoading(true);

    try {
      // 組合 API 的完整路徑
      const url = settings.apiUrl + "/AppChangePassword";
      const response = await fetch(url, {
        method: 'POST',

        headers: settings.httpHeader,
        body: JSON.stringify({
          password,
          newpwd,
          confpwd,
        }),

      });

      const data = await response.json();
      
      if (response.ok) {
        alert(globalData._words.success || "密碼更新成功");
      } else {
        alert(globalData._words.failed || "密碼更新失敗: " + (data.message || "未知錯誤"));
      }
    } catch (error) {
      console.error("更新密碼錯誤：", error);
      alert("發生錯誤，請稍後再試");
    }
    
    setLoading(false);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '400px', margin: '50px auto' }}>
      <h2>變更密碼</h2>
      <div style={{ marginBottom: '15px' }}>
        <label>舊密碼</label>
        <input 
          type="password" 
          value={password} 
          onChange={(e) => setPassword(e.target.value)} 
          style={{ width: '100%', padding: '8px', marginTop: '5px' }} 
        />
      </div>
      <div style={{ marginBottom: '15px' }}>
        <label>新密碼</label>
        <input 
          type="password" 
          value={newpwd} 
          onChange={(e) => setNewpwd(e.target.value)} 
          style={{ width: '100%', padding: '8px', marginTop: '5px' }} 
        />
      </div>
      <div style={{ marginBottom: '15px' }}>
        <label>確認新密碼</label>
        <input 
          type="password" 
          value={confpwd} 
          onChange={(e) => setConfpwd(e.target.value)} 
          style={{ width: '100%', padding: '8px', marginTop: '5px' }} 
        />
      </div>
      <button 
        onClick={handleChangePassword} 
        disabled={loading} 
        style={{
          width: '100%', 
          padding: '10px', 
          backgroundColor: '#007BFF', 
          color: '#fff', 
          border: 'none', 
          borderRadius: '4px'
        }}
      >
        {loading ? "處理中..." : "確認"}
      </button>
    </div>
  );
} 