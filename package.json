{"name": "patrolling", "main": "expo-router/entry", "license": "0BSD", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-picker/picker": "2.11.1", "@react-navigation/native": "^7.1.6", "expo": "53.0.17", "expo-camera": "~16.1.10", "expo-font": "~13.3.1", "expo-linking": "~7.1.7", "expo-location": "~18.1.6", "expo-router": "~5.1.3", "expo-splash-screen": "~0.30.10", "expo-sqlite": "~15.2.13", "expo-status-bar": "^2.2.3", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-nfc-manager": "^3.16.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "~0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "jest": "^29.2.1", "jest-expo": "~53.0.9", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "private": true}