import { CameraView, Camera, useCameraPermissions } from "expo-camera";
import React, { useState, useEffect, useLayoutEffect, useRef } from 'react';
import { ScrollView, View, StyleSheet, SafeAreaView, TouchableOpacity, TextInput, Text, Modal, Linking, StatusBar, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useAppContext } from "./context/AppContext";

// 掃描模組元件
// visible: 控制模組顯示/隱藏
// onBarcodeScanned: 掃描成功回調函數
// onStopScan: 停止掃描回調函數
const EditModal = ({ visible, onBarcodeScanned, onStopScan }:any) => {
  const [scanned, setScanned] = useState(false); // 掃描狀態標記

  return (
    <Modal
      transparent={false}
      animationType="slide"
      visible={visible}
    >
      <View style={styles2.modalContainer}>
        <View style={styles2.modalContent}>
            <CameraView
              onBarcodeScanned={scanned ? undefined : ({ type, data }) => {
                //setScanned(true);
                onBarcodeScanned(data); // Call onBarcodeScanned passed as prop
              }}
              barcodeScannerSettings={{
                barcodeTypes: ["qr", "pdf417", "code39", "code93", "code128"],
              }}
              style={StyleSheet.absoluteFillObject}
            />
        </View>

        <TouchableOpacity style={{width: '70%', backgroundColor: 'teal', marginTop: 30, padding: 10}} onPress={() => {onStopScan()}}>
          <Text style={{textAlign:'center', color:'#fff'}}>停止掃描</Text>
        </TouchableOpacity>
      </View>
    </Modal>
  );
}

export default function changeUrl() {
  const { db, settings, setSettings } = useAppContext();
  const navigation = useNavigation();
  const [isUri, setIsUri] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [inputValue, setInputValue] = useState(settings.apiUrl);
  const urlRef = useRef(settings.apiUrl);

  console.log('settings.apiUrl', settings.apiUrl);

  const [permission, requestPermission] = useCameraPermissions();

  useLayoutEffect(() => {
    navigation.setOptions({
      title: '變更伺服器網址',  // 或其他你想要的標題
      headerTitleStyle: { fontSize: 16, fontWeight: 'bold' }
    });
  }, [navigation]);

  // 開始掃描函數
  // 1. 請求相機權限
  // 2. 根據權限狀態決定是否開啟掃描模組
  const startScan = async () => {
    const {status} = await requestPermission();
    console.log('status', status)
    if (status === 'granted') {
        setModalVisible(true); // 開啟掃描模組
    } else {
      Alert.alert('Access denied') // 權限被拒絕提示
    }
  };

  const stopScan = () => {
    setIsUri(urlRef.current.toLowerCase().startsWith('http'));
    setModalVisible(false);
  };


  const handleBarcodeScanned = (data: string) => {
    setModalVisible(false);
    urlRef.current = data;
    setInputValue(data);
    setIsUri(data.toLowerCase().startsWith('http'));
  };


  // 儲存伺服器網址函數
  // 1. 驗證網址格式是否正確
  // 2. 若網址有變更，更新全局狀態和資料庫
  // 3. 返回上一頁
  const saveUrl = async () => {
    if (!urlRef.current.toLowerCase().startsWith('http')) {
      Alert.alert('請輸入正確的網址');
      return;
    }

    if (urlRef.current.toLowerCase() !== settings.apiUrl.toLowerCase()) {
      setSettings((prev: any) => ({
          ...prev,
          apiUrl: urlRef.current // 更新全局狀態
      }));

      if (!!db) {
        console.log('update userInfo set apiUrl=', urlRef.current);
        await db.runAsync('update userInfo set apiUrl=?', [urlRef.current]); // 更新資料庫
        console.log('updated');
      }
    }
    navigation.goBack(); // 返回上一頁
  };


  return (
    <SafeAreaView style={styles.container}>
      <View
        style={{width:'100%', height:200, marginTop: StatusBar.currentHeight, padding: 8}}
      >
        <Text style={{color:'#333', marginBottom: 8}} numberOfLines={10}>APP URL:
        </Text>
        <TextInput
            style={{color:'blue', flex: 1, fontSize: 16, backgroundColor: '#fff', width: '100%', justifyContent: 'flex-start', alignItems: 'center'}}
            value={inputValue}
            onChangeText={(text) => {
              setInputValue(text);
              urlRef.current = text;
            }}
            numberOfLines={5}
            multiline
            textAlignVertical="top"

          />
      </View>


      <View style={{ width: '100%', flex: 1, justifyContent: 'space-around', alignItems: 'center' }}>
        {inputValue && (
          <TouchableOpacity style={styles.touchable} onPress={() => saveUrl()}>
            <Text style={{textAlign:'center', color:'white'}}>OK</Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity style={styles.touchable} onPress={() => startScan()}>
          <Text style={{textAlign:'center', color:'#fff'}}>開始掃描</Text>
        </TouchableOpacity>
      </View>

      <EditModal
        visible={modalVisible}
        onBarcodeScanned={handleBarcodeScanned}
        onStopScan={stopScan}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#afd4f3',
    alignItems: 'center',
    justifyContent: 'center',
  },
  touchable: {
    backgroundColor: '#007BFF',
    padding: 10,
    width: '70%',
    borderRadius: 5,
    textAlign: 'center'
  },
});

const styles2 = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '100%',
    padding: 20,
    backgroundColor: '#fff',
    borderRadius: 10,
    flex: .6
  },
  modalField: {
    fontSize: 16,
    marginVertical: 10,
    borderBottomWidth: 1,
    borderColor: '#ccc',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    padding: 10,
    borderRadius: 5,
    backgroundColor: '#007BFF',
  },
  modalButtonText: {
    color: '#fff',
  },
});
